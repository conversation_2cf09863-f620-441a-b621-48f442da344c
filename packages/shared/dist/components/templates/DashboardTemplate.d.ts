/**
 * Dashboard Template
 *
 * A template for dashboard pages with header, sidebar, and content areas.
 */
import React from 'react';
export interface DashboardTemplateProps {
    /** The header content */
    header: React.ReactNode;
    /** The sidebar content */
    sidebar: React.ReactNode;
    /** The main content */
    children: React.ReactNode;
    /** Whether the sidebar is collapsed */
    sidebarCollapsed?: boolean;
    /** Function to toggle the sidebar */
    toggleSidebar?: () => void;
    /** Additional CSS class names */
    className?: string;
}
/**
 * Dashboard Template
 *
 * A template for dashboard pages with header, sidebar, and content areas.
 */
export declare const DashboardTemplate: React.FC<DashboardTemplateProps>;
export default DashboardTemplate;
//# sourceMappingURL=DashboardTemplate.d.ts.map