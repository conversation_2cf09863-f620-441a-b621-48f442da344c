import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { DashboardTemplate } from './DashboardTemplate';
import { Card } from '../molecules/Card';
import { Button } from '../atoms/Button';
const meta = {
    title: 'Templates/DashboardTemplate',
    component: DashboardTemplate,
    parameters: {
        layout: 'fullscreen',
    },
};
export default meta;
// Mock header component
const Header = () => (_jsxs("div", { style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
    }, children: [_jsx("div", { style: { fontWeight: 'bold', fontSize: '1.5rem' }, children: "ADHD Trading Dashboard" }), _jsxs("div", { children: [_jsx(Button, { variant: "text", size: "small", children: "Settings" }), _jsx(<PERSON><PERSON>, { variant: "text", size: "small", children: "Profile" }), _jsx(But<PERSON>, { variant: "text", size: "small", children: "Logout" })] })] }));
// Mock sidebar component
const Sidebar = ({ collapsed }) => (_jsxs("div", { style: { padding: collapsed ? '8px' : '16px' }, children: [!collapsed && _jsx("h3", { style: { marginBottom: '16px' }, children: "Navigation" }), _jsxs("ul", { children: [_jsx("li", { style: { marginBottom: '8px' }, children: collapsed ? '📊' : 'Dashboard' }), _jsx("li", { style: { marginBottom: '8px' }, children: collapsed ? '📈' : 'Trading' }), _jsx("li", { style: { marginBottom: '8px' }, children: collapsed ? '📝' : 'Journal' }), _jsx("li", { style: { marginBottom: '8px' }, children: collapsed ? '⚙️' : 'Settings' })] })] }));
// Mock content
const Content = () => (_jsxs("div", { children: [_jsx("h1", { style: { marginBottom: '24px' }, children: "Dashboard" }), _jsxs("div", { style: {
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
                gap: '16px',
            }, children: [_jsx(Card, { title: "Performance", children: _jsx("p", { children: "Your performance metrics will appear here." }) }), _jsx(Card, { title: "Recent Trades", children: _jsx("p", { children: "Your recent trades will appear here." }) }), _jsx(Card, { title: "Market Overview", children: _jsx("p", { children: "Market overview will appear here." }) }), _jsx(Card, { title: "Trading Plan", children: _jsx("p", { children: "Your trading plan will appear here." }) })] })] }));
export const Default = {
    args: {
        header: _jsx(Header, {}),
        sidebar: _jsx(Sidebar, {}),
        children: _jsx(Content, {}),
        sidebarCollapsed: false,
    },
};
export const CollapsedSidebar = {
    args: {
        header: _jsx(Header, {}),
        sidebar: _jsx(Sidebar, { collapsed: true }),
        children: _jsx(Content, {}),
        sidebarCollapsed: true,
    },
};
//# sourceMappingURL=DashboardTemplate.stories.js.map