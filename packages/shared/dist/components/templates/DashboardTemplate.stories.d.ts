/**
 * Dashboard Template Stories
 */
import { <PERSON>a, StoryObj } from '@storybook/react';
import { DashboardTemplate } from './DashboardTemplate';
declare const meta: Meta<typeof DashboardTemplate>;
export default meta;
type Story = StoryObj<typeof DashboardTemplate>;
export declare const Default: Story;
export declare const CollapsedSidebar: Story;
//# sourceMappingURL=DashboardTemplate.stories.d.ts.map