{"version": 3, "file": "TradeMetrics.js", "sourceRoot": "", "sources": ["../../../src/components/trade/TradeMetrics.tsx"], "names": [], "mappings": ";AAQA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAEvC,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAG1B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACvC,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;gBACb,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;sBAC7B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;mBACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;aAC1C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC3C,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAA;WACnB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;eACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;mBAC7B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAA4C;WAC/D,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,CACzC,QAAQ;IACN,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;IACtB,CAAC,CAAC,QAAQ;QACV,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;QACrB,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;eACjB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;;CAE/C,CAAC;AAcF,MAAM,YAAY,GAAgC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE;IAC3E,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,CACL,KAAC,gBAAgB,cACd,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAC3C,MAAC,UAAU,eACT,KAAC,WAAW,6BAAyB,EACrC,KAAC,WAAW,qBAAiB,KAFd,KAAK,CAGT,CACd,CAAC,GACe,CACpB,CAAC;IACJ,CAAC;IAED,OAAO,CACL,KAAC,gBAAgB,cACd,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAC9B,MAAC,UAAU,eACT,KAAC,WAAW,cAAE,MAAM,CAAC,KAAK,GAAe,EACzC,KAAC,WAAW,IAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,YAC9D,MAAM,CAAC,KAAK,GACD,KAJC,KAAK,CAKT,CACd,CAAC,GACe,CACpB,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,YAAY,CAAC"}