import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Select } from './Select';
import { ThemeProvider } from '../../theme/ThemeProvider';
const meta = {
    title: 'Atoms/Select',
    component: Select,
    tags: ['autodocs'],
    decorators: [
        (Story) => (_jsx(ThemeProvider, { children: _jsx("div", { style: { padding: '1rem', maxWidth: '400px' }, children: _jsx(Story, {}) }) })),
    ],
    parameters: {
        layout: 'centered',
    },
    argTypes: {
        size: {
            control: 'select',
            options: ['small', 'medium', 'large'],
        },
        disabled: {
            control: 'boolean',
        },
        required: {
            control: 'boolean',
        },
        success: {
            control: 'boolean',
        },
        loading: {
            control: 'boolean',
        },
        fullWidth: {
            control: 'boolean',
        },
        onChange: { action: 'changed' },
    },
};
export default meta;
// Sample options
const simpleOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
    { value: 'option4', label: 'Option 4' },
];
const groupedOptions = [
    { value: 'apple', label: 'Apple', group: 'Fruits' },
    { value: 'banana', label: 'Banana', group: 'Fruits' },
    { value: 'orange', label: 'Orange', group: 'Fruits' },
    { value: 'carrot', label: 'Carrot', group: 'Vegetables' },
    { value: 'potato', label: 'Potato', group: 'Vegetables' },
    { value: 'broccoli', label: 'Broccoli', group: 'Vegetables' },
    { value: 'chicken', label: 'Chicken', group: 'Meat' },
    { value: 'beef', label: 'Beef', group: 'Meat' },
    { value: 'pork', label: 'Pork', group: 'Meat' },
];
const optionsWithDisabled = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2', disabled: true },
    { value: 'option3', label: 'Option 3' },
    { value: 'option4', label: 'Option 4', disabled: true },
];
// Controlled component wrapper for interactive stories
const ControlledSelect = (args) => {
    const [value, setValue] = useState(args.value || '');
    return _jsx(Select, { ...args, value: value, onChange: setValue });
};
export const Default = {
    render: (args) => _jsx(ControlledSelect, { ...args }),
    args: {
        options: simpleOptions,
        placeholder: 'Select an option',
        value: '',
    },
};
export const WithLabel = {
    render: (args) => _jsx(ControlledSelect, { ...args }),
    args: {
        label: 'Favorite Option',
        options: simpleOptions,
        placeholder: 'Select an option',
        value: '',
    },
};
export const WithHelperText = {
    render: (args) => _jsx(ControlledSelect, { ...args }),
    args: {
        label: 'Favorite Option',
        options: simpleOptions,
        placeholder: 'Select an option',
        helperText: 'Choose your favorite option',
        value: '',
    },
};
export const WithError = {
    render: (args) => _jsx(ControlledSelect, { ...args }),
    args: {
        label: 'Favorite Option',
        options: simpleOptions,
        placeholder: 'Select an option',
        error: 'Please select a valid option',
        value: '',
    },
};
export const WithSuccess = {
    render: (args) => _jsx(ControlledSelect, { ...args }),
    args: {
        label: 'Favorite Option',
        options: simpleOptions,
        success: true,
        value: 'option1',
    },
};
export const Disabled = {
    render: (args) => _jsx(ControlledSelect, { ...args }),
    args: {
        label: 'Disabled Select',
        options: simpleOptions,
        placeholder: 'This select is disabled',
        disabled: true,
        value: '',
    },
};
export const Required = {
    render: (args) => _jsx(ControlledSelect, { ...args }),
    args: {
        label: 'Required Field',
        options: simpleOptions,
        placeholder: 'This field is required',
        required: true,
        value: '',
    },
};
export const WithIcon = {
    render: (args) => _jsx(ControlledSelect, { ...args }),
    args: {
        label: 'Category',
        options: simpleOptions,
        placeholder: 'Select a category',
        startIcon: _jsx("span", { children: "\uD83D\uDD0D" }),
        value: '',
    },
};
export const GroupedOptions = {
    render: (args) => _jsx(ControlledSelect, { ...args }),
    args: {
        label: 'Food Category',
        options: groupedOptions,
        placeholder: 'Select a food item',
        value: '',
    },
};
export const WithDisabledOptions = {
    render: (args) => _jsx(ControlledSelect, { ...args }),
    args: {
        label: 'Options',
        options: optionsWithDisabled,
        placeholder: 'Some options are disabled',
        value: '',
    },
};
export const Small = {
    render: (args) => _jsx(ControlledSelect, { ...args }),
    args: {
        label: 'Small Select',
        options: simpleOptions,
        placeholder: 'Small size',
        size: 'small',
        value: '',
    },
};
export const Large = {
    render: (args) => _jsx(ControlledSelect, { ...args }),
    args: {
        label: 'Large Select',
        options: simpleOptions,
        placeholder: 'Large size',
        size: 'large',
        value: '',
    },
};
export const AllVariants = {
    render: () => (_jsxs("div", { style: { display: 'flex', flexDirection: 'column', gap: '16px' }, children: [_jsx(Select, { label: "Default Select", options: simpleOptions, placeholder: "Default select", value: "", onChange: () => { } }), _jsx(Select, { label: "With Helper Text", options: simpleOptions, placeholder: "With helper text", helperText: "This is a helper text", value: "", onChange: () => { } }), _jsx(Select, { label: "With Error", options: simpleOptions, placeholder: "With error", error: "This field has an error", value: "", onChange: () => { } }), _jsx(Select, { label: "With Success", options: simpleOptions, placeholder: "With success", success: true, value: "option1", onChange: () => { } }), _jsx(Select, { label: "With Icon", options: simpleOptions, placeholder: "With icon", startIcon: _jsx("span", { children: "\uD83D\uDD0D" }), value: "", onChange: () => { } })] })),
};
//# sourceMappingURL=Select.stories.js.map