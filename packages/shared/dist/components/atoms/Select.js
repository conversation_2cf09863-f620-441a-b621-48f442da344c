import { jsxs as _jsxs, jsx as _jsx, Fragment as _Fragment } from "react/jsx-runtime";
/**
 * Select Component
 *
 * A customizable select component that follows the design system.
 */
import { useState } from 'react';
import styled, { css } from 'styled-components';
const SelectWrapper = styled.div `
  display: flex;
  flex-direction: column;
  width: ${({ fullWidth }) => (fullWidth ? '100%' : 'auto')};
  position: relative;
`;
const Label = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xxs};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
`;
const SelectContainer = styled.div `
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  border: 1px solid
    ${({ theme, hasError, hasSuccess, isFocused }) => {
    if (hasError)
        return theme.colors.error;
    if (hasSuccess)
        return theme.colors.success;
    if (isFocused)
        return theme.colors.primary;
    return theme.colors.border;
}};
  background-color: ${({ theme }) => theme.colors.surface};
  transition: all ${({ theme }) => theme.transitions?.fast || '0.2s ease'};

  ${({ disabled, theme }) => disabled &&
    css `
      opacity: 0.6;
      background-color: ${theme.colors.background};
      cursor: not-allowed;
    `}

  ${({ isFocused, theme, hasError, hasSuccess }) => isFocused &&
    css `
      box-shadow: 0 0 0 2px
        ${hasError
        ? `${theme.colors.error}33`
        : hasSuccess
            ? `${theme.colors.success}33`
            : `${theme.colors.primary}33`};
    `}

  ${({ $size }) => {
    switch ($size) {
        case 'small':
            return css `
          height: 32px;
        `;
        case 'large':
            return css `
          height: 48px;
        `;
        default:
            return css `
          height: 40px;
        `;
    }
}}
`;
const IconContainer = styled.div `
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 ${({ theme }) => theme.spacing.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const StyledSelect = styled.select `
  flex: 1;
  border: none;
  background: transparent;
  color: ${({ theme }) => theme.colors.textPrimary};
  width: 100%;
  outline: none;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right ${({ theme }) => theme.spacing.sm} center;
  background-size: 16px;
  padding-right: ${({ theme }) => theme.spacing.xl};

  &:disabled {
    cursor: not-allowed;
  }

  ${({ hasStartIcon }) => hasStartIcon &&
    css `
      padding-left: 0;
    `}

  ${({ $size, theme }) => {
    if ($size === 'small') {
        return css `
        font-size: ${theme.fontSizes.xs};
        padding: ${theme.spacing.xxs} ${theme.spacing.xs};
      `;
    }
    else if ($size === 'large') {
        return css `
        font-size: ${theme.fontSizes.md};
        padding: ${theme.spacing.sm} ${theme.spacing.md};
      `;
    }
    else {
        return css `
        font-size: ${theme.fontSizes.sm};
        padding: ${theme.spacing.xs} ${theme.spacing.sm};
      `;
    }
}}
`;
const HelperTextContainer = styled.div `
  display: flex;
  justify-content: space-between;
  margin-top: ${({ theme }) => theme.spacing.xxs};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme, hasError, hasSuccess }) => {
    if (hasError)
        return theme.colors.error;
    if (hasSuccess)
        return theme.colors.success;
    return theme.colors.textSecondary;
}};
`;
const OptionGroup = styled.optgroup `
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
/**
 * Select Component
 *
 * A customizable select component that follows the design system.
 */
export const Select = ({ options, value, onChange, disabled = false, error = '', name = '', id = '', className = '', required = false, placeholder = '', label = '', helperText = '', size = 'medium', fullWidth = true, loading = false, success = false, startIcon, ...rest }) => {
    const [isFocused, setIsFocused] = useState(false);
    const handleFocus = (e) => {
        setIsFocused(true);
        if (rest.onFocus) {
            rest.onFocus(e);
        }
    };
    const handleBlur = (e) => {
        setIsFocused(false);
        if (rest.onBlur) {
            rest.onBlur(e);
        }
    };
    // Group options by their group property if any
    const groupedOptions = {};
    const ungroupedOptions = [];
    options.forEach((option) => {
        if (option.group) {
            if (!groupedOptions[option.group]) {
                groupedOptions[option.group] = [];
            }
            groupedOptions[option.group].push(option);
        }
        else {
            ungroupedOptions.push(option);
        }
    });
    const hasGroups = Object.keys(groupedOptions).length > 0;
    return (_jsxs(SelectWrapper, { className: className, fullWidth: fullWidth, children: [label && (_jsxs(Label, { htmlFor: id, children: [label, required && ' *'] })), _jsxs(SelectContainer, { hasError: !!error, hasSuccess: !!success, disabled: !!(disabled || loading), "$size": size, hasStartIcon: !!startIcon, isFocused: !!isFocused, children: [startIcon && _jsx(IconContainer, { children: startIcon }), _jsxs(StyledSelect, { value: value, onChange: (e) => onChange(e.target.value), disabled: !!(disabled || loading), name: name, id: id, required: !!required, hasStartIcon: !!startIcon, "$size": size, onFocus: handleFocus, onBlur: handleBlur, ...rest, children: [placeholder && (_jsx("option", { value: "", disabled: true, children: placeholder })), hasGroups ? (_jsxs(_Fragment, { children: [ungroupedOptions.map((option) => (_jsx("option", { value: option.value, disabled: option.disabled, children: option.label }, option.value))), Object.entries(groupedOptions).map(([group, options]) => (_jsx(OptionGroup, { label: group, children: options.map((option) => (_jsx("option", { value: option.value, disabled: option.disabled, children: option.label }, option.value))) }, group)))] })) : (
                            // Render all options without groups
                            options.map((option) => (_jsx("option", { value: option.value, disabled: option.disabled, children: option.label }, option.value))))] })] }), (error || helperText) && (_jsx(HelperTextContainer, { hasError: !!error, hasSuccess: !!success, children: _jsx("div", { children: error || helperText }) }))] }));
};
//# sourceMappingURL=Select.js.map