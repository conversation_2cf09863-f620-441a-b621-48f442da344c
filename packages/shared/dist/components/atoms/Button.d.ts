/**
 * Button Component
 *
 * A customizable button component that follows the design system.
 */
import React from 'react';
export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'text' | 'success' | 'danger';
export type ButtonSize = 'small' | 'medium' | 'large';
export interface ButtonProps extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'size' | 'type'> {
    /** The content to display inside the button */
    children: React.ReactNode;
    /** The variant of the button */
    variant?: ButtonVariant;
    /** Whether the button is disabled */
    disabled?: boolean;
    /** Whether the button is in a loading state */
    loading?: boolean;
    /** The size of the button */
    size?: ButtonSize;
    /** Whether the button is full width */
    fullWidth?: boolean;
    /** Icon to display before the button text */
    startIcon?: React.ReactNode;
    /** Icon to display after the button text */
    endIcon?: React.ReactNode;
    /** Function called when the button is clicked */
    onClick?: () => void;
    /** Additional CSS class names */
    className?: string;
    /** Button type */
    type?: 'button' | 'submit' | 'reset';
}
/**
 * Button Component
 *
 * A customizable button component that follows the design system.
 */
export declare const Button: React.FC<ButtonProps>;
//# sourceMappingURL=Button.d.ts.map