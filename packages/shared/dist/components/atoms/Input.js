import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
/**
 * Input Component
 *
 * A customizable input component that follows the design system.
 */
import { useState, useRef } from 'react';
import styled, { css } from 'styled-components';
const InputWrapper = styled.div `
  display: flex;
  flex-direction: column;
  width: ${({ fullWidth }) => (fullWidth ? '100%' : 'auto')};
  position: relative;
`;
const Label = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xxs};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
`;
const InputContainer = styled.div `
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  border: 1px solid
    ${({ theme, hasError, hasSuccess, isFocused }) => {
    if (hasError)
        return theme.colors.error;
    if (hasSuccess)
        return theme.colors.success;
    if (isFocused)
        return theme.colors.primary;
    return theme.colors.border;
}};
  background-color: ${({ theme }) => theme.colors.surface};
  transition: all ${({ theme }) => theme.transitions?.fast || '0.2s ease'};

  ${({ disabled, theme }) => disabled &&
    css `
      opacity: 0.6;
      background-color: ${theme.colors.background};
      cursor: not-allowed;
    `}

  ${({ isFocused, theme, hasError, hasSuccess }) => isFocused &&
    css `
      box-shadow: 0 0 0 2px
        ${hasError
        ? `${theme.colors.error}33`
        : hasSuccess
            ? `${theme.colors.success}33`
            : `${theme.colors.primary}33`};
    `}

  ${({ $size }) => {
    switch ($size) {
        case 'small':
            return css `
          height: 32px;
        `;
        case 'large':
            return css `
          height: 48px;
        `;
        default:
            return css `
          height: 40px;
        `;
    }
}}
`;
const IconContainer = styled.div `
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 ${({ theme }) => theme.spacing.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const StyledInput = styled.input `
  flex: 1;
  border: none;
  background: transparent;
  color: ${({ theme }) => theme.colors.textPrimary};
  width: 100%;
  outline: none;

  &:disabled {
    cursor: not-allowed;
  }

  &::placeholder {
    color: ${({ theme }) => theme.colors.textDisabled};
  }

  ${({ hasStartIcon }) => hasStartIcon &&
    css `
      padding-left: 0;
    `}

  ${({ hasEndIcon }) => hasEndIcon &&
    css `
      padding-right: 0;
    `}

  ${({ $size, theme }) => {
    if ($size === 'small') {
        return css `
        font-size: ${theme.fontSizes.xs};
        padding: ${theme.spacing.xxs} ${theme.spacing.xs};
      `;
    }
    else if ($size === 'large') {
        return css `
        font-size: ${theme.fontSizes.md};
        padding: ${theme.spacing.sm} ${theme.spacing.md};
      `;
    }
    else {
        return css `
        font-size: ${theme.fontSizes.sm};
        padding: ${theme.spacing.xs} ${theme.spacing.sm};
      `;
    }
}}
`;
const ClearButton = styled.button `
  background: none;
  border: none;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.textDisabled};
  padding: 0 ${({ theme }) => theme.spacing.xs};
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: ${({ theme }) => theme.colors.textSecondary};
  }

  &:focus {
    outline: none;
  }
`;
const HelperTextContainer = styled.div `
  display: flex;
  justify-content: space-between;
  margin-top: ${({ theme }) => theme.spacing.xxs};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme, hasError, hasSuccess }) => {
    if (hasError)
        return theme.colors.error;
    if (hasSuccess)
        return theme.colors.success;
    return theme.colors.textSecondary;
}};
`;
/**
 * Input Component
 *
 * A customizable input component that follows the design system.
 */
export const Input = ({ value, onChange, placeholder = '', disabled = false, error = '', type = 'text', name = '', id = '', className = '', required = false, autoComplete = '', label = '', helperText = '', startIcon, endIcon, loading = false, success = false, clearable = false, onClear, maxLength, showCharCount = false, size = 'medium', fullWidth = false, ...rest }) => {
    const [isFocused, setIsFocused] = useState(false);
    const inputRef = useRef(null);
    const handleClear = () => {
        if (onClear) {
            onClear();
        }
        else {
            onChange('');
        }
        if (inputRef.current) {
            inputRef.current.focus();
        }
    };
    const handleFocus = (e) => {
        setIsFocused(true);
        if (rest.onFocus) {
            rest.onFocus(e);
        }
    };
    const handleBlur = (e) => {
        setIsFocused(false);
        if (rest.onBlur) {
            rest.onBlur(e);
        }
    };
    // Clear button should only show when there's a value and the input is not disabled
    const showClearButton = clearable && value && !disabled;
    // Character count
    const charCount = value?.length || 0;
    const showCount = showCharCount || (maxLength !== undefined && maxLength > 0);
    return (_jsxs(InputWrapper, { className: className, fullWidth: fullWidth, children: [label && (_jsxs(Label, { htmlFor: id, children: [label, required && ' *'] })), _jsxs(InputContainer, { hasError: !!error, hasSuccess: !!success, disabled: !!disabled, "$size": size, hasStartIcon: !!startIcon, hasEndIcon: !!(endIcon || showClearButton), isFocused: !!isFocused, children: [startIcon && _jsx(IconContainer, { children: startIcon }), _jsx(StyledInput, { ref: inputRef, type: type, value: value, onChange: (e) => onChange(e.target.value), placeholder: placeholder, disabled: !!(disabled || loading), name: name, id: id, required: !!required, autoComplete: autoComplete, hasStartIcon: !!startIcon, hasEndIcon: !!(endIcon || showClearButton), "$size": size, maxLength: maxLength, onFocus: handleFocus, onBlur: handleBlur, ...rest }), showClearButton && (_jsx(ClearButton, { type: "button", onClick: handleClear, tabIndex: -1, children: "\u2715" })), endIcon && _jsx(IconContainer, { children: endIcon })] }), (error || helperText || showCount) && (_jsxs(HelperTextContainer, { hasError: !!error, hasSuccess: !!success, children: [_jsx("div", { children: error || helperText }), showCount && (_jsxs("div", { children: [charCount, maxLength !== undefined && `/${maxLength}`] }))] }))] }));
};
//# sourceMappingURL=Input.js.map