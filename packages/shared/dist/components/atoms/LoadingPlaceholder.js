import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled, { css, keyframes } from 'styled-components';
// Size styles
const sizeStyles = {
    small: css `
    height: 100px;
  `,
    medium: css `
    height: 200px;
  `,
    large: css `
    height: 300px;
  `,
    custom: (props) => css `
    height: ${props.customHeight};
    width: ${props.customWidth || '100%'};
  `,
};
// Variant styles
const variantStyles = {
    default: css `
    background-color: ${({ theme }) => theme.colors.background};
    border-radius: ${({ theme }) => theme.borderRadius.md};
  `,
    card: css `
    background-color: ${({ theme }) => theme.colors.surface};
    border-radius: ${({ theme }) => theme.borderRadius.md};
    box-shadow: ${({ theme }) => theme.shadows.sm};
  `,
    text: css `
    background-color: transparent;
    height: auto !important;
    min-height: 1.5em;
  `,
    list: css `
    background-color: ${({ theme }) => theme.colors.background};
    border-radius: ${({ theme }) => theme.borderRadius.sm};
    margin-bottom: ${({ theme }) => theme.spacing.sm};
  `,
};
// Spinner animation
const spin = keyframes `
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;
const Container = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  /* Apply size styles */
  ${({ size, customHeight, customWidth }) => {
    if (size === 'custom') {
        return sizeStyles.custom({ customHeight, customWidth });
    }
    return sizeStyles[size];
}}

  /* Apply variant styles */
  ${({ variant }) => variantStyles[variant]}
`;
const Spinner = styled.div `
  width: 32px;
  height: 32px;
  border: 3px solid ${({ theme }) => theme.colors.background};
  border-top: 3px solid ${({ theme }) => theme.colors.primary};
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;
const Text = styled.div `
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;
/**
 * Loading Placeholder Component
 *
 * A component for displaying loading states with customizable appearance.
 */
export const LoadingPlaceholder = ({ variant = 'default', size = 'medium', height = '200px', width = '', text = 'Loading...', showSpinner = true, className = '', }) => {
    return (_jsxs(Container, { variant: variant, size: size, customHeight: height, customWidth: width, className: className, children: [showSpinner && _jsx(Spinner, {}), text && _jsx(Text, { children: text })] }));
};
//# sourceMappingURL=LoadingPlaceholder.js.map