import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Dual Time Display Component
 *
 * Displays time in both NY and local timezone with proper formatting.
 * Optimized for international traders working with NY sessions.
 */
import { useState, useEffect } from 'react';
import styled from 'styled-components';
import { getCurrentDualTime, formatTimeForMobile, getTimeUntilNYTime, convertSessionToDualTime, } from '../../utils/timeZoneUtils';
const TimeContainer = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ format }) => (format === 'mobile' ? '4px' : '8px')};
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 600;
`;
const NYTime = styled.span `
  color: #3b82f6;
  font-size: inherit;
`;
const LocalTime = styled.span `
  color: #10b981;
  font-size: inherit;
`;
const Separator = styled.span `
  color: #6b7280;
  font-size: inherit;
`;
const Timezone = styled.span `
  color: #9ca3af;
  font-size: 0.85em;
  font-weight: 500;
`;
const LiveIndicator = styled.span `
  color: #ef4444;
  font-size: 0.75em;
  font-weight: bold;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;
const CountdownContainer = styled.div `
  display: flex;
  align-items: center;
  gap: 8px;
`;
const CountdownValue = styled.span `
  color: #f59e0b;
  font-weight: bold;
`;
const CountdownLabel = styled.span `
  color: #9ca3af;
  font-size: 0.9em;
`;
/**
 * Current Time Display Component
 */
const CurrentTimeDisplay = ({ format, showLive, updateInterval }) => {
    const [dualTime, setDualTime] = useState(getCurrentDualTime());
    useEffect(() => {
        const timer = setInterval(() => {
            setDualTime(getCurrentDualTime());
        }, updateInterval * 1000);
        return () => clearInterval(timer);
    }, [updateInterval]);
    if (format === 'mobile') {
        return (_jsxs(TimeContainer, { format: format, children: [_jsx("span", { children: formatTimeForMobile(dualTime) }), showLive && _jsx(LiveIndicator, { children: "LIVE" })] }));
    }
    if (format === 'compact') {
        return (_jsxs(TimeContainer, { format: format, children: [_jsx(NYTime, { children: dualTime.nyTime }), _jsx(Separator, { children: "|" }), _jsx(LocalTime, { children: dualTime.localTime }), showLive && _jsx(LiveIndicator, { children: "LIVE" })] }));
    }
    return (_jsxs(TimeContainer, { format: format, children: [_jsx(NYTime, { children: dualTime.nyTime }), _jsx(Timezone, { children: dualTime.nyTimezone }), _jsx(Separator, { children: "|" }), _jsx(LocalTime, { children: dualTime.localTime }), _jsx(Timezone, { children: dualTime.localTimezone }), showLive && _jsx(LiveIndicator, { children: "LIVE" })] }));
};
/**
 * Static Time Display Component
 */
const StaticTimeDisplay = ({ nyTime, format }) => {
    const dualTime = getCurrentDualTime();
    const sessionTime = convertSessionToDualTime(nyTime, nyTime);
    if (format === 'mobile') {
        return (_jsx(TimeContainer, { format: format, children: _jsxs("span", { children: [sessionTime.localStart, " \uD83C\uDDEE\uD83C\uDDEA | ", nyTime, " \uD83C\uDDFA\uD83C\uDDF8"] }) }));
    }
    if (format === 'compact') {
        return (_jsxs(TimeContainer, { format: format, children: [_jsx(NYTime, { children: nyTime }), _jsx(Separator, { children: "|" }), _jsx(LocalTime, { children: sessionTime.localStart })] }));
    }
    return (_jsxs(TimeContainer, { format: format, children: [_jsx(NYTime, { children: nyTime }), _jsx(Timezone, { children: dualTime.nyTimezone }), _jsx(Separator, { children: "|" }), _jsx(LocalTime, { children: sessionTime.localStart }), _jsx(Timezone, { children: dualTime.localTimezone })] }));
};
/**
 * Countdown Display Component
 */
const CountdownDisplay = ({ targetNYTime, format, updateInterval }) => {
    const [timeUntil, setTimeUntil] = useState(getTimeUntilNYTime(targetNYTime));
    useEffect(() => {
        const timer = setInterval(() => {
            setTimeUntil(getTimeUntilNYTime(targetNYTime));
        }, updateInterval * 1000);
        return () => clearInterval(timer);
    }, [targetNYTime, updateInterval]);
    if (format === 'mobile') {
        return (_jsxs(CountdownContainer, { children: [_jsx(CountdownValue, { children: timeUntil.formatted }), _jsxs(CountdownLabel, { children: ["until ", targetNYTime] })] }));
    }
    return (_jsxs(CountdownContainer, { children: [_jsx(CountdownLabel, { children: "Next in:" }), _jsx(CountdownValue, { children: timeUntil.formatted }), _jsxs(CountdownLabel, { children: ["(", targetNYTime, " NY)"] })] }));
};
/**
 * Session Time Display Component
 */
const SessionTimeDisplay = ({ sessionStart, sessionEnd, format }) => {
    const sessionTime = convertSessionToDualTime(sessionStart, sessionEnd);
    if (format === 'mobile') {
        return (_jsx(TimeContainer, { format: format, children: _jsx("span", { children: sessionTime.formatted }) }));
    }
    if (format === 'compact') {
        return (_jsxs(TimeContainer, { format: format, children: [_jsxs(NYTime, { children: [sessionStart, "-", sessionEnd] }), _jsx(Separator, { children: "|" }), _jsxs(LocalTime, { children: [sessionTime.localStart, "-", sessionTime.localEnd] })] }));
    }
    return (_jsxs(TimeContainer, { format: format, children: [_jsx("div", { children: _jsxs(NYTime, { children: [sessionStart, "-", sessionEnd, " NY"] }) }), _jsx(Separator, { children: "|" }), _jsx("div", { children: _jsxs(LocalTime, { children: [sessionTime.localStart, "-", sessionTime.localEnd, " Local"] }) })] }));
};
/**
 * Main Dual Time Display Component
 */
export const DualTimeDisplay = (props) => {
    const { mode = 'current', nyTime, targetNYTime, sessionStart, sessionEnd, format = 'desktop', showLive = false, className, updateInterval = 1, } = props;
    const containerProps = {
        className,
        style: {
            fontSize: format === 'mobile' ? '14px' : format === 'compact' ? '13px' : '14px',
        },
    };
    switch (mode) {
        case 'static':
            if (!nyTime) {
                console.warn('DualTimeDisplay: nyTime is required for static mode');
                return null;
            }
            return (_jsx("div", { ...containerProps, children: _jsx(StaticTimeDisplay, { nyTime: nyTime, format: format }) }));
        case 'countdown':
            if (!targetNYTime) {
                console.warn('DualTimeDisplay: targetNYTime is required for countdown mode');
                return null;
            }
            return (_jsx("div", { ...containerProps, children: _jsx(CountdownDisplay, { targetNYTime: targetNYTime, format: format, updateInterval: updateInterval }) }));
        case 'session':
            if (!sessionStart || !sessionEnd) {
                console.warn('DualTimeDisplay: sessionStart and sessionEnd are required for session mode');
                return null;
            }
            return (_jsx("div", { ...containerProps, children: _jsx(SessionTimeDisplay, { sessionStart: sessionStart, sessionEnd: sessionEnd, format: format }) }));
        case 'current':
        default:
            return (_jsx("div", { ...containerProps, children: _jsx(CurrentTimeDisplay, { format: format, showLive: showLive, updateInterval: updateInterval }) }));
    }
};
export default DualTimeDisplay;
//# sourceMappingURL=DualTimeDisplay.js.map