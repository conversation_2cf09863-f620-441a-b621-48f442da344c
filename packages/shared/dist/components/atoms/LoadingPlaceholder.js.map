{"version": 3, "file": "LoadingPlaceholder.js", "sourceRoot": "", "sources": ["../../../src/components/atoms/LoadingPlaceholder.tsx"], "names": [], "mappings": ";AAMA,OAAO,MAAM,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAsB3D,cAAc;AACd,MAAM,UAAU,GAAG;IACjB,KAAK,EAAE,GAAG,CAAA;;GAET;IACD,MAAM,EAAE,GAAG,CAAA;;GAEV;IACD,KAAK,EAAE,GAAG,CAAA;;GAET;IACD,MAAM,EAAE,CAAC,KAAqD,EAAE,EAAE,CAAC,GAAG,CAAA;cAC1D,KAAK,CAAC,YAAY;aACnB,KAAK,CAAC,WAAW,IAAI,MAAM;GACrC;CACF,CAAC;AAEF,iBAAiB;AACjB,MAAM,aAAa,GAAG;IACpB,OAAO,EAAE,GAAG,CAAA;wBACU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;qBACzC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;GACtD;IACD,IAAI,EAAE,GAAG,CAAA;wBACa,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;qBACtC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;kBACvC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;GAC9C;IACD,IAAI,EAAE,GAAG,CAAA;;;;GAIR;IACD,IAAI,EAAE,GAAG,CAAA;wBACa,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;qBACzC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;qBACpC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;GACjD;CACF,CAAC;AAEF,oBAAoB;AACpB,MAAM,IAAI,GAAG,SAAS,CAAA;;;;;;;CAOrB,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAK1B;;;;;;;IAOE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,EAAE,EAAE;IACxC,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;QACtB,OAAO,UAAU,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC,CAAC;IAC1D,CAAC;IACD,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;AAC1B,CAAC;;;IAGC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC;CAC1C,CAAC;AAEF,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAA;;;sBAGJ,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;0BAClC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;;eAE9C,IAAI;mBACA,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAA;WACZ,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;eACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;CAC/C,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAsC,CAAC,EACpE,OAAO,GAAG,SAAsC,EAChD,IAAI,GAAG,QAAkC,EACzC,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,EAAE,EACV,IAAI,GAAG,YAAY,EACnB,WAAW,GAAG,IAAI,EAClB,SAAS,GAAG,EAAE,GACf,EAAE,EAAE;IACH,OAAO,CACL,MAAC,SAAS,IACR,OAAO,EAAE,OAAO,EAChB,IAAI,EAAE,IAAI,EACV,YAAY,EAAE,MAAM,EACpB,WAAW,EAAE,KAAK,EAClB,SAAS,EAAE,SAAS,aAEnB,WAAW,IAAI,KAAC,OAAO,KAAG,EAC1B,IAAI,IAAI,KAAC,IAAI,cAAE,IAAI,GAAQ,IAClB,CACb,CAAC;AACJ,CAAC,CAAC"}