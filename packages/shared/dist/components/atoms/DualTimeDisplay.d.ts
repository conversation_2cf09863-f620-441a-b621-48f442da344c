/**
 * Dual Time Display Component
 *
 * Displays time in both NY and local timezone with proper formatting.
 * Optimized for international traders working with NY sessions.
 */
import React from 'react';
export interface DualTimeDisplayProps {
    /** Display mode */
    mode?: 'current' | 'static' | 'countdown' | 'session';
    /** Static NY time to display (for static mode) */
    nyTime?: string;
    /** Target NY time for countdown */
    targetNYTime?: string;
    /** Session start and end times (for session mode) */
    sessionStart?: string;
    sessionEnd?: string;
    /** Display format */
    format?: 'mobile' | 'desktop' | 'compact';
    /** Show live indicator */
    showLive?: boolean;
    /** Custom className */
    className?: string;
    /** Update interval in seconds (default: 1) */
    updateInterval?: number;
}
/**
 * Main Dual Time Display Component
 */
export declare const DualTimeDisplay: React.FC<DualTimeDisplayProps>;
export default DualTimeDisplay;
//# sourceMappingURL=DualTimeDisplay.d.ts.map