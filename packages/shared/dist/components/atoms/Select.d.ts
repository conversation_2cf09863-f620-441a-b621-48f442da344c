/**
 * Select Component
 *
 * A customizable select component that follows the design system.
 */
import React from 'react';
export interface SelectOption {
    /** The value of the option */
    value: string;
    /** The label to display for the option */
    label: string;
    /** Whether the option is disabled */
    disabled?: boolean;
    /** Optional group for the option */
    group?: string;
}
export type SelectSize = 'small' | 'medium' | 'large';
type CustomSelectHTMLAttributes = Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange' | 'size'> & {
    size?: SelectSize;
};
export interface SelectProps extends CustomSelectHTMLAttributes {
    /** The options to display */
    options: SelectOption[];
    /** The selected value */
    value: string;
    /** Function called when the selection changes */
    onChange: (value: string) => void;
    /** Whether the select is disabled */
    disabled?: boolean;
    /** The error message */
    error?: string;
    /** The select name */
    name?: string;
    /** The select id */
    id?: string;
    /** Additional CSS class names */
    className?: string;
    /** Whether the select is required */
    required?: boolean;
    /** Placeholder text (first option) */
    placeholder?: string;
    /** Label for the select */
    label?: string;
    /** Helper text to display below the select */
    helperText?: string;
    /** Size of the select */
    size?: 'small' | 'medium' | 'large';
    /** Whether the select should be full width */
    fullWidth?: boolean;
    /** Whether the select is in a loading state */
    loading?: boolean;
    /** Whether the select is in a success state */
    success?: boolean;
    /** Icon to display at the start of the select */
    startIcon?: React.ReactNode;
}
/**
 * Select Component
 *
 * A customizable select component that follows the design system.
 */
export declare const Select: React.FC<SelectProps>;
export {};
//# sourceMappingURL=Select.d.ts.map