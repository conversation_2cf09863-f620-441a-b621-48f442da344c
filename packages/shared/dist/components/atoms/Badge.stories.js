import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Badge } from './Badge';
import { ThemeProvider } from '../../theme/ThemeProvider';
const meta = {
    title: 'Atoms/Badge',
    component: Badge,
    tags: ['autodocs'],
    decorators: [
        (Story) => (_jsx(ThemeProvider, { children: _jsx(Story, {}) })),
    ],
    parameters: {
        layout: 'centered',
    },
    argTypes: {
        variant: {
            control: 'select',
            options: [
                'default',
                'primary',
                'secondary',
                'success',
                'warning',
                'error',
                'info',
                'neutral',
            ],
        },
        size: {
            control: 'select',
            options: ['small', 'medium', 'large'],
        },
        solid: {
            control: 'boolean',
        },
        rounded: {
            control: 'boolean',
        },
        dot: {
            control: 'boolean',
        },
        counter: {
            control: 'boolean',
        },
        outlined: {
            control: 'boolean',
        },
        inline: {
            control: 'boolean',
        },
        onClick: { action: 'clicked' },
    },
};
export default meta;
export const Default = {
    args: {
        children: 'Default Badge',
        variant: 'default',
        size: 'medium',
        solid: false,
    },
};
export const Primary = {
    args: {
        children: 'Primary Badge',
        variant: 'primary',
        size: 'medium',
        solid: false,
    },
};
export const Success = {
    args: {
        children: 'Success Badge',
        variant: 'success',
        size: 'medium',
        solid: false,
    },
};
export const Warning = {
    args: {
        children: 'Warning Badge',
        variant: 'warning',
        size: 'medium',
        solid: false,
    },
};
export const Error = {
    args: {
        children: 'Error Badge',
        variant: 'error',
        size: 'medium',
        solid: false,
    },
};
export const Solid = {
    args: {
        children: 'Solid Badge',
        variant: 'primary',
        size: 'medium',
        solid: true,
    },
};
export const Outlined = {
    args: {
        children: 'Outlined Badge',
        variant: 'primary',
        size: 'medium',
        outlined: true,
    },
};
export const Rounded = {
    args: {
        children: 'Rounded Badge',
        variant: 'primary',
        size: 'medium',
        rounded: true,
    },
};
export const Dot = {
    args: {
        children: '',
        variant: 'error',
        dot: true,
    },
};
export const Counter = {
    args: {
        children: 42,
        variant: 'primary',
        counter: true,
        solid: true,
        rounded: true,
    },
};
export const CounterWithMax = {
    args: {
        children: 999,
        variant: 'primary',
        counter: true,
        solid: true,
        rounded: true,
        max: 99,
    },
};
export const WithIcons = {
    args: {
        children: 'Badge with Icons',
        variant: 'primary',
        startIcon: _jsx("span", { children: "\uD83D\uDD14" }),
        endIcon: _jsx("span", { children: "\u2713" }),
    },
};
export const Small = {
    args: {
        children: 'Small Badge',
        variant: 'primary',
        size: 'small',
        solid: false,
    },
};
export const Large = {
    args: {
        children: 'Large Badge',
        variant: 'primary',
        size: 'large',
        solid: false,
    },
};
export const Clickable = {
    args: {
        children: 'Clickable Badge',
        variant: 'primary',
        size: 'medium',
        solid: false,
        onClick: () => alert('Badge clicked!'),
    },
};
export const AllVariants = {
    render: () => (_jsxs("div", { style: { display: 'flex', gap: '8px', flexWrap: 'wrap' }, children: [_jsx(Badge, { variant: "default", children: "Default" }), _jsx(Badge, { variant: "primary", children: "Primary" }), _jsx(Badge, { variant: "secondary", children: "Secondary" }), _jsx(Badge, { variant: "success", children: "Success" }), _jsx(Badge, { variant: "warning", children: "Warning" }), _jsx(Badge, { variant: "error", children: "Error" }), _jsx(Badge, { variant: "info", children: "Info" }), _jsx(Badge, { variant: "neutral", children: "Neutral" })] })),
};
export const AllStyles = {
    render: () => (_jsxs("div", { style: { display: 'flex', flexDirection: 'column', gap: '16px' }, children: [_jsxs("div", { style: { display: 'flex', gap: '8px', flexWrap: 'wrap', alignItems: 'center' }, children: [_jsx(Badge, { variant: "primary", children: "Default" }), _jsx(Badge, { variant: "primary", solid: true, children: "Solid" }), _jsx(Badge, { variant: "primary", outlined: true, children: "Outlined" }), _jsx(Badge, { variant: "primary", rounded: true, children: "Rounded" }), _jsx(Badge, { variant: "primary", dot: true, children: '' }), _jsx(Badge, { variant: "primary", counter: true, solid: true, rounded: true, children: "42" })] }), _jsxs("div", { style: { display: 'flex', gap: '8px', flexWrap: 'wrap', alignItems: 'center' }, children: [_jsx(Badge, { variant: "primary", size: "small", children: "Small" }), _jsx(Badge, { variant: "primary", size: "medium", children: "Medium" }), _jsx(Badge, { variant: "primary", size: "large", children: "Large" })] }), _jsxs("div", { style: { display: 'flex', gap: '8px', flexWrap: 'wrap', alignItems: 'center' }, children: [_jsx(Badge, { variant: "primary", startIcon: _jsx("span", { children: "\uD83D\uDD14" }), children: "With Start Icon" }), _jsx(Badge, { variant: "primary", endIcon: _jsx("span", { children: "\u2713" }), children: "With End Icon" }), _jsx(Badge, { variant: "primary", startIcon: _jsx("span", { children: "\uD83D\uDD14" }), endIcon: _jsx("span", { children: "\u2713" }), children: "Both Icons" })] })] })),
};
//# sourceMappingURL=Badge.stories.js.map