/**
 * Badge Component
 *
 * A customizable badge component for displaying status, labels, or counts.
 */
import React from 'react';
export type BadgeVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'neutral';
export type BadgeSize = 'small' | 'medium' | 'large';
export interface BadgeProps {
    /** The content to display inside the badge */
    children: React.ReactNode;
    /** The variant of the badge */
    variant?: BadgeVariant;
    /** The size of the badge */
    size?: BadgeSize;
    /** Whether the badge has a solid background */
    solid?: boolean;
    /** Additional CSS class names */
    className?: string;
    /** Inline styles */
    style?: React.CSSProperties;
    /** Optional click handler */
    onClick?: () => void;
    /** Whether the badge is rounded (pill-shaped) */
    rounded?: boolean;
    /** Whether the badge is a dot (no content) */
    dot?: boolean;
    /** Whether the badge is a counter */
    counter?: boolean;
    /** Whether the badge is outlined */
    outlined?: boolean;
    /** Icon to display before the badge text */
    startIcon?: React.ReactNode;
    /** Icon to display after the badge text */
    endIcon?: React.ReactNode;
    /** Maximum number to display (for counter badges) */
    max?: number;
    /** Whether the badge is inline */
    inline?: boolean;
}
/**
 * Badge Component
 *
 * A customizable badge component for displaying status, labels, or counts.
 */
export declare const Badge: React.FC<BadgeProps>;
//# sourceMappingURL=Badge.d.ts.map