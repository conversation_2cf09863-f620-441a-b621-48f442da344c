import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * F1Container Component
 *
 * Base container component following F1 racing theme.
 * Provides consistent layout, error boundaries, and loading states.
 *
 * PROVEN PATTERN: Extracted from successful TradingDashboard, TradeAnalysis,
 * and TradingPlan refactors.
 *
 * FEATURES:
 * - F1 racing theme consistency
 * - Built-in error boundaries
 * - Loading state management
 * - Responsive layout
 * - Performance optimized
 */
import { Suspense } from 'react';
import styled, { css } from 'styled-components';
import { spacing } from '../../../theme/tokens';
const Container = styled.div `
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: ${({ $maxWidth }) => (typeof $maxWidth === 'number' ? `${$maxWidth}px` : $maxWidth)};
  margin: 0 auto;
  min-height: ${({ $variant }) => ($variant === 'dashboard' ? '100vh' : 'auto')};

  /* Padding based on size */
  ${({ $padding }) => {
    const paddingMap = {
        sm: spacing.sm,
        md: spacing.md,
        lg: spacing.lg,
        xl: spacing.xl,
    };
    return css `
      padding: ${paddingMap[$padding || 'lg']};
    `;
}}

  /* Background based on variant */
  ${({ $background, theme }) => {
    const backgroundMap = {
        default: theme.colors.background,
        surface: theme.colors.surface,
        elevated: theme.colors.elevated,
    };
    return css `
      background: ${backgroundMap[$background || 'default']};
    `;
}}

  /* Variant-specific styles */
  ${({ $variant }) => {
    switch ($variant) {
        case 'dashboard':
            return css `
          gap: 24px;
          padding-top: 0;
        `;
        case 'form':
            return css `
          gap: 16px;
          max-width: 800px;
        `;
        case 'analysis':
            return css `
          gap: 20px;
          max-width: 1400px;
        `;
        case 'settings':
            return css `
          gap: 16px;
          max-width: 1000px;
        `;
        default:
            return css `
          gap: 16px;
        `;
    }
}}

  /* Animation support */
  ${({ $animated }) => $animated &&
    css `
      transition: all 0.3s ease-in-out;

      &.entering {
        opacity: 0;
        transform: translateY(20px);
      }

      &.entered {
        opacity: 1;
        transform: translateY(0);
      }
    `}
`;
const LoadingContainer = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;
  color: #9ca3af;
`;
const LoadingSpinner = styled.div `
  width: 40px;
  height: 40px;
  border: 3px solid #4b5563;
  border-top: 3px solid #dc2626;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;
const ErrorContainer = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid #f44336;
  border-radius: 8px;
  color: #f44336;
  text-align: center;
  gap: 16px;
`;
const ErrorIcon = styled.div `
  font-size: 48px;
  opacity: 0.8;
`;
const ErrorMessage = styled.div `
  font-size: 16px;
  font-weight: 500;
`;
const RetryButton = styled.button `
  padding: 8px 16px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s ease;

  &:hover {
    background: #d32f2f;
  }
`;
/**
 * Default Loading Fallback
 */
const DefaultLoadingFallback = () => (_jsxs(LoadingContainer, { children: [_jsx(LoadingSpinner, {}), _jsx("div", { children: "Loading..." })] }));
/**
 * Default Error Fallback
 */
const DefaultErrorFallback = ({ error, onRetry, }) => (_jsxs(ErrorContainer, { children: [_jsx(ErrorIcon, { children: "\u26A0\uFE0F" }), _jsx(ErrorMessage, { children: error }), onRetry && _jsx(RetryButton, { onClick: onRetry, children: "Retry" })] }));
/**
 * F1Container Component
 *
 * Base container component that provides consistent layout, error handling,
 * and loading states following the F1 racing theme.
 *
 * @example
 * ```typescript
 * // Dashboard container
 * <F1Container variant="dashboard" maxWidth={1400}>
 *   <DashboardContent />
 * </F1Container>
 *
 * // Form container with loading
 * <F1Container variant="form" isLoading={isSubmitting}>
 *   <TradeForm />
 * </F1Container>
 *
 * // Analysis container with error handling
 * <F1Container variant="analysis" error={error}>
 *   <AnalysisContent />
 * </F1Container>
 * ```
 */
export const F1Container = (props) => {
    const { children, variant = 'dashboard', maxWidth = '100%', padding = 'lg', isLoading = false, error = null, loadingFallback, errorFallback, className, animated = true, background = 'default', } = props;
    // Ensure proper typing for styled component props
    const styledProps = {
        $variant: variant,
        $maxWidth: maxWidth,
        $padding: padding,
        $animated: animated,
        $background: background,
    };
    // Show error state
    if (error) {
        return (_jsx(Container, { ...styledProps, className: className, children: errorFallback || _jsx(DefaultErrorFallback, { error: error }) }));
    }
    // Show loading state
    if (isLoading) {
        return (_jsx(Container, { ...styledProps, className: className, children: loadingFallback || _jsx(DefaultLoadingFallback, {}) }));
    }
    // Show content with Suspense boundary
    return (_jsx(Container, { ...styledProps, className: className, children: _jsx(Suspense, { fallback: loadingFallback || _jsx(DefaultLoadingFallback, {}), children: children }) }));
};
export default F1Container;
//# sourceMappingURL=F1Container.js.map