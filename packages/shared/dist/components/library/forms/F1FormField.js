import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled, { css } from 'styled-components';
import { spacing, fontSizes } from '../../../theme/tokens';
const FieldContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ $size }) => {
    const sizeMap = {
        sm: spacing.xs,
        md: spacing.sm,
        lg: spacing.md,
    };
    return sizeMap[$size || 'md'];
}};
`;
const Label = styled.label `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};

  /* Variant-specific styling */
  ${({ $variant }) => {
    switch ($variant) {
        case 'trading':
            return css `
          text-transform: uppercase;
          letter-spacing: 0.025em;
        `;
        case 'analysis':
            return css `
          font-weight: 500;
        `;
        default:
            return css ``;
    }
}}

  /* Required indicator */
  ${({ $required, theme }) => $required &&
    css `
      &::after {
        content: '*';
        color: ${theme.colors?.primary || '#dc2626'};
        margin-left: 2px;
      }
    `}
`;
const InputContainer = styled.div `
  position: relative;
  display: flex;
  align-items: center;

  ${({ $disabled }) => $disabled &&
    css `
      opacity: 0.6;
      pointer-events: none;
    `}
`;
const baseInputStyles = css `
  width: 100%;
  border: 1px solid
    ${({ $hasError, theme }) => ($hasError ? theme.colors?.error || '#f44336' : '#4b5563')};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  background: ${({ theme }) => theme.colors?.background || '#111827'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-family: inherit;
  transition: all 0.2s ease;

  /* Size-based styling */
  ${({ $size }) => {
    const sizeMap = {
        sm: css `
        padding: ${spacing.xs} ${spacing.sm};
        font-size: ${fontSizes.sm};
      `,
        md: css `
        padding: ${spacing.sm} ${spacing.md};
        font-size: ${fontSizes.md};
      `,
        lg: css `
        padding: ${spacing.md} ${spacing.lg};
        font-size: ${fontSizes.lg};
      `,
    };
    return sizeMap[$size || 'md'];
}}

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
  }

  &:disabled {
    background: #374151;
    color: #9ca3af;
    cursor: not-allowed;
  }

  &::placeholder {
    color: #6b7280;
  }
`;
const Input = styled.input `
  ${baseInputStyles}
`;
const Select = styled.select `
  ${baseInputStyles}
  cursor: pointer;

  option {
    background: ${({ theme }) => theme.colors?.background || '#111827'};
    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  }
`;
const TextArea = styled.textarea `
  ${baseInputStyles}
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
`;
const PrefixContainer = styled.div `
  position: absolute;
  left: 12px;
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  pointer-events: none;
  z-index: 1;
`;
const SuffixContainer = styled.div `
  position: absolute;
  right: 12px;
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
`;
const ErrorMessage = styled.div `
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.error || '#f44336'};
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;
const HelpText = styled.div `
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
`;
const ValidationIndicator = styled.div `
  position: absolute;
  right: 8px;
  display: flex;
  align-items: center;

  ${({ $validating }) => $validating &&
    css `
      &::after {
        content: '';
        width: 12px;
        height: 12px;
        border: 2px solid #4b5563;
        border-top: 2px solid #dc2626;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    `}

  ${({ $valid, $validating }) => !$validating &&
    css `
      color: ${$valid ? '#22c55e' : '#f44336'};
      &::after {
        content: '${$valid ? '✓' : '✗'}';
      }
    `}
`;
/**
 * F1FormField Component
 *
 * Standardized form field that integrates with useFormField hook
 * and provides consistent F1-themed styling.
 *
 * @example
 * ```typescript
 * const symbolField = useFormField({
 *   initialValue: 'MNQ',
 *   required: true,
 *   validationRules: [validationRules.required()],
 * });
 *
 * <F1FormField
 *   label="Symbol"
 *   field={symbolField}
 *   type="text"
 *   placeholder="Enter symbol"
 *   variant="trading"
 *   required={true}
 * />
 * ```
 */
export const F1FormField = (props) => {
    const { label, field, type = 'text', placeholder, required = false, disabled = false, helpText, options = [], inputProps = {}, className, size = 'md', variant = 'default', prefix, suffix, } = props;
    const hasError = !!(field.error && field.touched);
    const showValidation = field.touched && !field.validating;
    const renderInput = () => {
        const commonProps = {
            id: inputProps.id || label.toLowerCase().replace(/\s+/g, '-'),
            value: field.value,
            onChange: field.setValue,
            onBlur: () => field.setTouched(true),
            disabled,
            placeholder,
            $hasError: hasError,
            $size: size,
            ...inputProps,
        };
        switch (type) {
            case 'select':
                return (_jsxs(Select, { ...commonProps, children: [placeholder && (_jsx("option", { value: "", disabled: true, children: placeholder })), options.map((option) => (_jsx("option", { value: option.value, children: option.label }, option.value)))] }));
            case 'textarea':
                return _jsx(TextArea, { ...commonProps });
            default:
                return _jsx(Input, { ...commonProps, type: type });
        }
    };
    return (_jsxs(FieldContainer, { "$size": size, className: className, children: [_jsx(Label, { "$required": required, "$variant": variant, htmlFor: inputProps.id || label.toLowerCase().replace(/\s+/g, '-'), children: label }), _jsxs(InputContainer, { "$hasError": hasError, "$disabled": disabled, children: [prefix && _jsx(PrefixContainer, { children: prefix }), renderInput(), suffix && _jsx(SuffixContainer, { children: suffix }), showValidation && (_jsx(ValidationIndicator, { "$valid": field.valid, "$validating": field.validating }))] }), hasError && _jsxs(ErrorMessage, { children: ["\u26A0\uFE0F ", field.error] }), helpText && !hasError && _jsx(HelpText, { children: helpText })] }));
};
export default F1FormField;
//# sourceMappingURL=F1FormField.js.map