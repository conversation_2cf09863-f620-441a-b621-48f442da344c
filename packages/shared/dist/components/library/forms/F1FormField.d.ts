/**
 * F1FormField Component
 *
 * Standardized form field component that integrates with useFormField hook
 * and provides consistent F1-themed styling.
 *
 * PROVEN PATTERN: Extracted from QuickTradeForm and TradingPlan form patterns
 *
 * FEATURES:
 * - Integration with useFormField hook
 * - F1 racing theme consistency
 * - Built-in validation display
 * - Multiple input types support
 * - Accessibility compliant
 */
import React, { ReactNode } from 'react';
import { UseFormFieldReturn } from '../../../hooks/useFormField';
export interface F1FormFieldProps {
    /** Field label */
    label: string;
    /** Form field hook return */
    field: UseFormFieldReturn;
    /** Input type */
    type?: 'text' | 'number' | 'email' | 'password' | 'tel' | 'url' | 'select' | 'textarea';
    /** Placeholder text */
    placeholder?: string;
    /** Whether field is required */
    required?: boolean;
    /** Whether field is disabled */
    disabled?: boolean;
    /** Help text */
    helpText?: string;
    /** Select options (for select type) */
    options?: Array<{
        value: string | number;
        label: string;
    }>;
    /** Custom input props */
    inputProps?: Record<string, any>;
    /** Custom className */
    className?: string;
    /** Field size */
    size?: 'sm' | 'md' | 'lg';
    /** Field variant */
    variant?: 'default' | 'trading' | 'analysis';
    /** Icon or prefix content */
    prefix?: ReactNode;
    /** Suffix content */
    suffix?: ReactNode;
}
/**
 * F1FormField Component
 *
 * Standardized form field that integrates with useFormField hook
 * and provides consistent F1-themed styling.
 *
 * @example
 * ```typescript
 * const symbolField = useFormField({
 *   initialValue: 'MNQ',
 *   required: true,
 *   validationRules: [validationRules.required()],
 * });
 *
 * <F1FormField
 *   label="Symbol"
 *   field={symbolField}
 *   type="text"
 *   placeholder="Enter symbol"
 *   variant="trading"
 *   required={true}
 * />
 * ```
 */
export declare const F1FormField: React.FC<F1FormFieldProps>;
export default F1FormField;
//# sourceMappingURL=F1FormField.d.ts.map