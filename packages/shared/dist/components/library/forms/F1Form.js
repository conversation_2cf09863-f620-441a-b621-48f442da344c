import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled, { css } from 'styled-components';
import { spacing } from '../../../theme/tokens';
const FormContainer = styled.form `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  position: relative;

  /* Variant-specific styling */
  ${({ $variant }) => {
    switch ($variant) {
        case 'quick':
            return css `
          padding: ${spacing.lg};
          max-width: 600px;
        `;
        case 'detailed':
            return css `
          padding: ${spacing.xl};
          max-width: 800px;
        `;
        case 'modal':
            return css `
          padding: ${spacing.lg};
          max-width: 500px;
          margin: 0 auto;
        `;
        case 'inline':
            return css `
          padding: ${spacing.md};
          background: transparent;
          border: none;
        `;
        default:
            return css `
          padding: ${spacing.lg};
        `;
    }
}}

  /* F1 Racing accent */
  ${({ $showAccent, theme }) => $showAccent &&
    css `
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(
          90deg,
          ${theme.colors?.primary || '#dc2626'},
          ${theme.colors?.primaryDark || '#b91c1c'},
          ${theme.colors?.primary || '#dc2626'}
        );
        border-radius: ${theme.borderRadius?.lg || '8px'} ${theme.borderRadius?.lg || '8px'} 0 0;
      }
    `}

  /* Disabled state */
  ${({ $disabled }) => $disabled &&
    css `
      opacity: 0.6;
      pointer-events: none;
    `}
`;
const FormHeader = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const FormTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;
const FormSubtitle = styled.div `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  font-weight: 500;
`;
const FormContent = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const FormMessage = styled.div `
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};

  ${({ $type }) => {
    switch ($type) {
        case 'error':
            return css `
          background: rgba(244, 67, 54, 0.1);
          border: 1px solid #f44336;
          color: #f44336;
        `;
        case 'success':
            return css `
          background: rgba(34, 197, 94, 0.1);
          border: 1px solid #22c55e;
          color: #22c55e;
        `;
        case 'info':
            return css `
          background: rgba(59, 130, 246, 0.1);
          border: 1px solid #3b82f6;
          color: #3b82f6;
        `;
    }
}}
`;
const LoadingOverlay = styled.div `
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  z-index: 10;
`;
const LoadingSpinner = styled.div `
  width: 32px;
  height: 32px;
  border: 3px solid #4b5563;
  border-top: 3px solid #dc2626;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;
const AutoSaveIndicator = styled.div `
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  opacity: ${({ $visible }) => ($visible ? 1 : 0)};
  transition: opacity 0.3s ease;
`;
/**
 * F1Form Component
 *
 * Standardized form component that provides consistent styling,
 * validation support, and F1 racing theme.
 *
 * @example
 * ```typescript
 * // Quick trade form
 * <F1Form
 *   title="🏎️ Quick Trade Entry"
 *   variant="quick"
 *   showAccent={true}
 *   onSubmit={handleSubmit}
 *   isSubmitting={isLoading}
 *   error={error}
 *   autoSave={true}
 * >
 *   <TradeFormFields />
 * </F1Form>
 *
 * // Detailed form
 * <F1Form
 *   title="Trade Analysis"
 *   subtitle="Comprehensive trade details"
 *   variant="detailed"
 *   onSubmit={handleSubmit}
 * >
 *   <DetailedFormFields />
 * </F1Form>
 * ```
 */
export const F1Form = (props) => {
    const { children, onSubmit, title, subtitle, isSubmitting = false, error = null, success = null, variant = 'quick', showAccent = true, className, disabled = false, autoSave = false, autoSaveInterval = 30000, } = props;
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (onSubmit && !isSubmitting && !disabled) {
            await onSubmit(e);
        }
    };
    return (_jsxs(FormContainer, { "$variant": variant, "$showAccent": showAccent, "$disabled": disabled, className: className, onSubmit: handleSubmit, noValidate: true, children: [isSubmitting && (_jsx(LoadingOverlay, { children: _jsx(LoadingSpinner, {}) })), autoSave && (_jsx(AutoSaveIndicator, { "$visible": !isSubmitting, children: "Auto-save enabled" })), (title || subtitle) && (_jsxs(FormHeader, { children: [title && _jsx(FormTitle, { children: title }), subtitle && _jsx(FormSubtitle, { children: subtitle })] })), error && _jsxs(FormMessage, { "$type": "error", children: ["\u26A0\uFE0F ", error] }), success && _jsxs(FormMessage, { "$type": "success", children: ["\u2705 ", success] }), _jsx(FormContent, { children: children })] }));
};
export default F1Form;
//# sourceMappingURL=F1Form.js.map