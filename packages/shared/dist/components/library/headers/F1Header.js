import { Fragment as _Fragment, jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import styled, { css } from 'styled-components';
import { fontSizes } from '../../../theme/tokens';
const HeaderContainer = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'} 0;
  border-bottom: 2px solid #4b5563;
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '16px'};

  /* Variant-specific spacing */
  ${({ $variant }) => {
    switch ($variant) {
        case 'dashboard':
            return css `
          padding: 24px 0;
          margin-bottom: 24px;
        `;
        case 'form':
            return css `
          padding: 16px 0;
          margin-bottom: 16px;
        `;
        default:
            return css `
          padding: 20px 0;
          margin-bottom: 20px;
        `;
    }
}}
`;
const TitleSection = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;
const MainTitle = styled.h1 `
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  letter-spacing: -0.025em;
  text-transform: uppercase;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  /* Variant-specific sizing */
  ${({ $variant }) => {
    switch ($variant) {
        case 'dashboard':
            return css `
          font-size: ${fontSizes.xxxl};
        `;
        case 'analysis':
            return css `
          font-size: ${fontSizes.xxl};
        `;
        case 'form':
            return css `
          font-size: ${fontSizes.xl};
        `;
        default:
            return css `
          font-size: ${fontSizes.xxl};
        `;
    }
}}

  /* F1 Racing accent */
  span {
    color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  }
`;
const Subtitle = styled.div `
  font-size: ${fontSizes.sm};
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;
const ActionsSection = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  flex-wrap: wrap;
`;
const StatusIndicator = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;

  /* Status-based styling */
  ${({ $isLive, $variant }) => {
    if ($isLive) {
        return css `
        background: rgba(220, 38, 38, 0.1);
        border: 1px solid #dc2626;
        color: #dc2626;
      `;
    }
    else if ($variant === 'active') {
        return css `
        background: rgba(34, 197, 94, 0.1);
        border: 1px solid #22c55e;
        color: #22c55e;
      `;
    }
    else {
        return css `
        background: rgba(156, 163, 175, 0.1);
        border: 1px solid #9ca3af;
        color: #9ca3af;
      `;
    }
}}
`;
const StatusDot = styled.div `
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: ${({ $isLive }) => ($isLive ? '#dc2626' : '#22c55e')};

  ${({ $isLive }) => $isLive &&
    css `
      animation: pulse 2s infinite;

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }
    `}
`;
const RefreshButton = styled.button `
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  background: transparent;
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  border: 1px solid #4b5563;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  cursor: pointer;
  font-weight: 500;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  transition: all 0.2s ease;
  min-width: 100px;
  position: relative;

  &:hover {
    background: #4b5563;
    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
    border-color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Loading spinner */
  ${({ $isRefreshing }) => $isRefreshing &&
    css `
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin: -8px 0 0 -8px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    `}
`;
const CustomActions = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;
/**
 * F1Header Component
 *
 * Standardized header component that provides consistent branding
 * and functionality across all features.
 *
 * @example
 * ```typescript
 * // Dashboard header
 * <F1Header
 *   title="Trading 2025 Dashboard"
 *   subtitle="Live Trading Session"
 *   variant="dashboard"
 *   isLive={true}
 *   onRefresh={handleRefresh}
 *   isRefreshing={isLoading}
 * />
 *
 * // Analysis header
 * <F1Header
 *   title="Trade Analysis"
 *   subtitle="Performance Metrics & Insights"
 *   variant="analysis"
 *   statusText="LIVE DATA"
 *   onRefresh={refreshData}
 * />
 *
 * // Form header with custom actions
 * <F1Header
 *   title="Trade Entry"
 *   subtitle="Quick Trade Form"
 *   variant="form"
 *   actions={<SaveButton />}
 * />
 * ```
 */
export const F1Header = (props) => {
    const { title, subtitle, isLive = false, liveText = 'LIVE SESSION', statusText, onRefresh, isRefreshing = false, actions, variant = 'dashboard', className, } = props;
    const displayStatusText = isLive ? liveText : statusText;
    return (_jsxs(HeaderContainer, { "$variant": variant, className: className, children: [_jsxs(TitleSection, { children: [_jsx(MainTitle, { "$variant": variant, children: variant === 'dashboard' ? (_jsxs(_Fragment, { children: ["\uD83C\uDFCE\uFE0F ", title.replace('Trading', 'TRADING').replace('Dashboard', 'DASHBOARD')] })) : (title) }), subtitle && _jsx(Subtitle, { children: subtitle })] }), _jsxs(ActionsSection, { children: [displayStatusText && (_jsxs(StatusIndicator, { "$isLive": isLive, "$variant": !isLive && statusText ? 'active' : undefined, children: [_jsx(StatusDot, { "$isLive": isLive }), displayStatusText] })), onRefresh && (_jsx(RefreshButton, { onClick: onRefresh, disabled: isRefreshing, "$isRefreshing": isRefreshing, children: isRefreshing ? 'Refreshing...' : 'Refresh' })), actions && _jsx(CustomActions, { children: actions })] })] }));
};
export default F1Header;
//# sourceMappingURL=F1Header.js.map