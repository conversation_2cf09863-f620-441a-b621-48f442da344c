import { jsx as _jsx } from "react/jsx-runtime";
import { ErrorBoundary } from './ErrorBoundary';
/**
 * Unified Error Boundary
 *
 * A wrapper around the base ErrorBoundary component that provides a simpler API
 * for common use cases.
 */
export const UnifiedErrorBoundary = ({ isAppLevel = false, isFeatureBoundary = false, children, ...props }) => {
    // Determine the boundary type based on props
    const boundaryType = isAppLevel ? 'app' : isFeatureBoundary ? 'feature' : 'component';
    // Set appropriate defaults based on boundary type
    const defaultProps = {
        resetOnPropsChange: boundaryType !== 'app', // App-level boundaries should not reset on props change
        resetOnUnmount: boundaryType !== 'app', // App-level boundaries should not reset on unmount
        isFeatureBoundary: boundaryType === 'feature',
    };
    return _jsx(ErrorBoundary, { ...defaultProps, ...props, children: children });
};
/**
 * App Error Boundary
 *
 * A specialized error boundary for the application level.
 */
export const AppErrorBoundary = (props) => {
    return _jsx(UnifiedErrorBoundary, { isAppLevel: true, ...props });
};
/**
 * Feature Error Boundary
 *
 * A specialized error boundary for feature modules.
 */
export const FeatureErrorBoundary = ({ featureName, children, ...props }) => {
    return (_jsx(UnifiedErrorBoundary, { isFeatureBoundary: true, name: featureName, children: children, ...props }));
};
export default UnifiedErrorBoundary;
//# sourceMappingURL=UnifiedErrorBoundary.js.map