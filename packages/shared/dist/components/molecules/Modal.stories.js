import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Modal } from './Modal';
import { Button } from '../atoms/Button';
import { Input } from '../atoms/Input';
import { ThemeProvider } from '../../theme/ThemeProvider';
const meta = {
    title: 'Molecules/Modal',
    component: Modal,
    tags: ['autodocs'],
    decorators: [
        (Story) => (_jsx(ThemeProvider, { children: _jsx(Story, {}) })),
    ],
    parameters: {
        layout: 'centered',
    },
    argTypes: {
        size: {
            control: 'select',
            options: ['small', 'medium', 'large', 'fullscreen'],
        },
        closeOnOutsideClick: {
            control: 'boolean',
        },
        showCloseButton: {
            control: 'boolean',
        },
        hasFooter: {
            control: 'boolean',
        },
        primaryActionDisabled: {
            control: 'boolean',
        },
        primaryActionLoading: {
            control: 'boolean',
        },
        secondaryActionDisabled: {
            control: 'boolean',
        },
        centered: {
            control: 'boolean',
        },
        hasBackdrop: {
            control: 'boolean',
        },
        scrollable: {
            control: 'boolean',
        },
        onClose: { action: 'closed' },
        onPrimaryAction: { action: 'primary action clicked' },
        onSecondaryAction: { action: 'secondary action clicked' },
    },
};
export default meta;
// Wrapper component to control modal state
const ModalWrapper = (args) => {
    const [isOpen, setIsOpen] = useState(false);
    return (_jsxs(_Fragment, { children: [_jsx(Button, { onClick: () => setIsOpen(true), children: "Open Modal" }), _jsx(Modal, { ...args, isOpen: isOpen, onClose: () => setIsOpen(false), onPrimaryAction: () => {
                    args.onPrimaryAction?.();
                    if (!args.primaryActionLoading) {
                        setIsOpen(false);
                    }
                }, onSecondaryAction: () => {
                    args.onSecondaryAction?.();
                    setIsOpen(false);
                } })] }));
};
export const Default = {
    render: (args) => _jsx(ModalWrapper, { ...args }),
    args: {
        title: 'Modal Title',
        children: _jsx("p", { children: "This is a basic modal with a title and content." }),
        size: 'medium',
        primaryActionText: 'Confirm',
        secondaryActionText: 'Cancel',
    },
};
export const WithForm = {
    render: (args) => _jsx(ModalWrapper, { ...args }),
    args: {
        title: 'Edit Profile',
        children: (_jsxs("div", { style: { display: 'flex', flexDirection: 'column', gap: '16px' }, children: [_jsx(Input, { label: "Name", value: "John Doe", onChange: () => { }, fullWidth: true }), _jsx(Input, { label: "Email", value: "<EMAIL>", onChange: () => { }, fullWidth: true }), _jsx(Input, { label: "Bio", value: "Frontend developer with a passion for UI/UX design.", onChange: () => { }, fullWidth: true })] })),
        size: 'medium',
        primaryActionText: 'Save Changes',
        secondaryActionText: 'Cancel',
    },
};
export const Small = {
    render: (args) => _jsx(ModalWrapper, { ...args }),
    args: {
        title: 'Small Modal',
        children: _jsx("p", { children: "This is a small modal with minimal content." }),
        size: 'small',
        primaryActionText: 'OK',
    },
};
export const Large = {
    render: (args) => _jsx(ModalWrapper, { ...args }),
    args: {
        title: 'Large Modal',
        children: (_jsxs("div", { children: [_jsx("p", { children: "This is a large modal with more content." }), _jsx("p", { children: "It can fit more information and is suitable for complex forms or detailed information." }), _jsx("p", { children: "The width is set to 800px by default." })] })),
        size: 'large',
        primaryActionText: 'Confirm',
        secondaryActionText: 'Cancel',
    },
};
export const WithoutFooter = {
    render: (args) => _jsx(ModalWrapper, { ...args }),
    args: {
        title: 'Information',
        children: (_jsxs("div", { children: [_jsx("p", { children: "This modal doesn't have a footer with action buttons." }), _jsx("p", { children: "It's useful for displaying information that doesn't require user action." })] })),
        hasFooter: false,
    },
};
export const WithCustomFooter = {
    render: (args) => _jsx(ModalWrapper, { ...args }),
    args: {
        title: 'Custom Footer',
        children: _jsx("p", { children: "This modal has a custom footer with multiple buttons." }),
        footer: (_jsxs("div", { style: { display: 'flex', gap: '8px', width: '100%', justifyContent: 'space-between' }, children: [_jsx(Button, { variant: "outline", size: "small", children: "Back" }), _jsxs("div", { style: { display: 'flex', gap: '8px' }, children: [_jsx(Button, { variant: "outline", children: "Cancel" }), _jsx(Button, { variant: "secondary", children: "Save Draft" }), _jsx(Button, { children: "Publish" })] })] })),
    },
};
export const LoadingAction = {
    render: (args) => _jsx(ModalWrapper, { ...args }),
    args: {
        title: 'Processing',
        children: _jsx("p", { children: "Click the primary action button to see the loading state." }),
        primaryActionText: 'Submit',
        secondaryActionText: 'Cancel',
        primaryActionLoading: true,
    },
};
export const LongContent = {
    render: (args) => _jsx(ModalWrapper, { ...args }),
    args: {
        title: 'Scrollable Content',
        children: (_jsx("div", { children: Array.from({ length: 20 }).map((_, index) => (_jsxs("p", { children: ["This is paragraph ", index + 1, ". The modal has scrollable content when it exceeds the available height."] }, index))) })),
        primaryActionText: 'Confirm',
        secondaryActionText: 'Cancel',
    },
};
export const Confirmation = {
    render: (args) => _jsx(ModalWrapper, { ...args }),
    args: {
        title: 'Confirm Action',
        children: _jsx("p", { children: "Are you sure you want to delete this item? This action cannot be undone." }),
        primaryActionText: 'Delete',
        secondaryActionText: 'Cancel',
        size: 'small',
    },
};
export const WithoutTitle = {
    render: (args) => _jsx(ModalWrapper, { ...args }),
    args: {
        children: _jsx("p", { children: "This modal doesn't have a title, only a close button in the header." }),
        primaryActionText: 'Confirm',
        secondaryActionText: 'Cancel',
    },
};
//# sourceMappingURL=Modal.stories.js.map