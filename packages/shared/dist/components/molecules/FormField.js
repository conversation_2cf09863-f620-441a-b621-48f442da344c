import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * FormField Component
 *
 * A component that combines a label with an input, select, or other form control.
 */
import React from 'react';
import styled from 'styled-components';
const FieldContainer = styled.div `
  display: flex;
  flex-direction: column;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const Label = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  margin-bottom: ${({ theme }) => theme.spacing.xxs};
  color: ${({ theme, hasError }) => (hasError ? theme.colors.error : theme.colors.textPrimary)};

  .required-indicator {
    color: ${({ theme }) => theme.colors.error};
    margin-left: ${({ theme }) => theme.spacing.xxs};
  }
`;
const HelperText = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme, hasError }) => (hasError ? theme.colors.error : theme.colors.textSecondary)};
  margin-top: ${({ theme }) => theme.spacing.xxs};
`;
/**
 * FormField Component
 *
 * A component that combines a label with an input, select, or other form control.
 */
export const FormField = ({ children, label, helperText, required = false, error, className, id, ...rest }) => {
    // Generate a unique ID if none is provided
    const fieldId = id || `field-${Math.random().toString(36).substr(2, 9)}`;
    // Clone the child element to pass the id
    const childElement = React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
            return React.cloneElement(child, {
                id: fieldId,
                required,
                error,
            });
        }
        return child;
    });
    return (_jsxs(FieldContainer, { className: className, ...rest, children: [_jsxs(Label, { htmlFor: fieldId, hasError: !!error, children: [label, required && _jsx("span", { className: "required-indicator", children: "*" })] }), childElement, (helperText || error) && _jsx(HelperText, { hasError: !!error, children: error || helperText })] }));
};
//# sourceMappingURL=FormField.js.map