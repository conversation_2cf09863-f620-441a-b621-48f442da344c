import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Table Component
 *
 * A customizable table component that follows the design system.
 *
 * @deprecated This component is being split into smaller, focused components.
 * Use TradeTable, TradeTableRow, TradeTableFilters, and TradeTableColumns instead.
 */
import { useMemo } from 'react';
import styled, { css } from 'styled-components';
import { Button } from '../atoms/Button';
const TableContainer = styled.div `
  width: 100%;
  overflow: auto;
  ${({ height }) => height && `height: ${height};`}
  ${({ scrollable }) => scrollable && `overflow-x: auto;`}
`;
const StyledTable = styled.table `
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: ${({ theme }) => theme.fontSizes.sm};

  ${({ bordered, theme }) => bordered &&
    css `
      border: 1px solid ${theme.colors.border};
      border-radius: ${theme.borderRadius.sm};
    `}

  ${({ compact, theme }) => compact
    ? css `
          th,
          td {
            padding: ${theme.spacing.xs} ${theme.spacing.sm};
          }
        `
    : css `
          th,
          td {
            padding: ${theme.spacing.sm} ${theme.spacing.md};
          }
        `}
`;
const TableHeader = styled.thead `
  ${({ stickyHeader }) => stickyHeader &&
    css `
      position: sticky;
      top: 0;
      z-index: 1;
    `}
`;
const TableHeaderRow = styled.tr `
  background-color: ${({ theme }) => theme.colors.background};
`;
const TableHeaderCell = styled.th `
  text-align: ${({ align }) => align || 'left'};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.textSecondary};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  white-space: nowrap;
  ${({ width }) => width && `width: ${width};`}

  ${({ sortable }) => sortable &&
    css `
      cursor: pointer;
      user-select: none;

      &:hover {
        background-color: ${({ theme }) => theme.colors.background}aa;
      }
    `}

  ${({ isSorted, theme }) => isSorted &&
    css `
      color: ${theme.colors.primary};
    `}
`;
const SortIcon = styled.span `
  display: inline-block;
  margin-left: ${({ theme }) => theme.spacing.xs};

  &::after {
    content: '${({ direction }) => (direction === 'asc' ? '↑' : direction === 'desc' ? '↓' : '↕')}';
  }
`;
const TableBody = styled.tbody ``;
const TableRow = styled.tr `
  ${({ striped, theme, isSelected }) => striped &&
    !isSelected &&
    css `
      &:nth-child(even) {
        background-color: ${theme.colors.background}50;
      }
    `}

  ${({ hoverable, theme, isSelected }) => hoverable &&
    !isSelected &&
    css `
      &:hover {
        background-color: ${theme.colors.background}aa;
      }
    `}

  ${({ isSelected, theme }) => isSelected &&
    css `
      background-color: ${theme.colors.primary}15;
    `}

  ${({ isClickable }) => isClickable &&
    css `
      cursor: pointer;
    `}
`;
const TableCell = styled.td `
  text-align: ${({ align }) => align || 'left'};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const EmptyState = styled.div `
  padding: ${({ theme }) => theme.spacing.xl};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const PaginationContainer = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.md} 0;
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;
const PageInfo = styled.div `
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const PaginationControls = styled.div `
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const PageSizeSelector = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-right: ${({ theme }) => theme.spacing.md};
`;
const LoadingOverlay = styled.div `
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${({ theme }) => `${theme.colors.background}80`};
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
`;
const LoadingSpinner = styled.div `
  width: 32px;
  height: 32px;
  border: 3px solid ${({ theme }) => theme.colors.background};
  border-top: 3px solid ${({ theme }) => theme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;
/**
 * Table Component
 *
 * A customizable table component that follows the design system.
 */
export function Table({ columns, data, isLoading = false, bordered = true, striped = true, hoverable = true, compact = false, stickyHeader = false, height, onRowClick, isRowSelected, onSort, sortColumn, sortDirection, pagination = false, currentPage = 1, pageSize = 10, totalRows = 0, onPageChange, onPageSizeChange, className, emptyMessage = 'No data available', scrollable = true, }) {
    // Filter out hidden columns
    const visibleColumns = useMemo(() => columns.filter((col) => !col.hidden), [columns]);
    // Calculate pagination
    const totalPages = useMemo(() => Math.ceil(totalRows / pageSize), [totalRows, pageSize]);
    const paginatedData = useMemo(() => {
        if (!pagination)
            return data;
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        // If we have totalRows, assume data is already paginated from the server
        if (totalRows > 0 && data.length <= pageSize) {
            return data;
        }
        return data.slice(startIndex, endIndex);
    }, [data, pagination, currentPage, pageSize, totalRows]);
    // Handle sort
    const handleSort = (columnId) => {
        if (!onSort)
            return;
        const newDirection = sortColumn === columnId && sortDirection === 'asc' ? 'desc' : 'asc';
        onSort(columnId, newDirection);
    };
    // Handle page change
    const handlePageChange = (page) => {
        if (page < 1 || page > totalPages || !onPageChange)
            return;
        onPageChange(page);
    };
    return (_jsxs("div", { style: { position: 'relative' }, children: [isLoading && (_jsx(LoadingOverlay, { children: _jsx(LoadingSpinner, {}) })), _jsx(TableContainer, { height: height, scrollable: scrollable, children: _jsxs(StyledTable, { bordered: bordered, striped: striped, compact: compact, className: className, children: [_jsx(TableHeader, { stickyHeader: stickyHeader, children: _jsx(TableHeaderRow, { children: visibleColumns.map((column) => (_jsxs(TableHeaderCell, { sortable: column.sortable, isSorted: sortColumn === column.id, align: column.align, width: column.width, onClick: () => column.sortable && handleSort(column.id), children: [column.header, column.sortable && (_jsx(SortIcon, { direction: sortColumn === column.id ? sortDirection : undefined }))] }, column.id))) }) }), _jsx(TableBody, { children: paginatedData.length > 0 ? (paginatedData.map((row, rowIndex) => (_jsx(TableRow, { hoverable: hoverable, striped: striped, isSelected: isRowSelected ? isRowSelected(row, rowIndex) : false, isClickable: !!onRowClick, onClick: () => onRowClick && onRowClick(row, rowIndex), children: visibleColumns.map((column) => (_jsx(TableCell, { align: column.align, children: column.cell(row, rowIndex) }, column.id))) }, rowIndex)))) : (_jsx("tr", { children: _jsx("td", { colSpan: visibleColumns.length, children: _jsx(EmptyState, { children: emptyMessage }) }) })) })] }) }), pagination && totalPages > 0 && (_jsxs(PaginationContainer, { children: [_jsxs(PageInfo, { children: ["Showing ", Math.min((currentPage - 1) * pageSize + 1, totalRows), " to", ' ', Math.min(currentPage * pageSize, totalRows), " of ", totalRows, " entries"] }), _jsxs("div", { style: { display: 'flex', alignItems: 'center' }, children: [onPageSizeChange && (_jsxs(PageSizeSelector, { children: [_jsx("span", { children: "Show" }), _jsx("select", { value: pageSize, onChange: (e) => onPageSizeChange(Number(e.target.value)), style: {
                                            padding: '4px 8px',
                                            borderRadius: '4px',
                                            border: '1px solid #ccc',
                                        }, children: [10, 25, 50, 100].map((size) => (_jsx("option", { value: size, children: size }, size))) }), _jsx("span", { children: "entries" })] })), _jsxs(PaginationControls, { children: [_jsx(Button, { size: "small", variant: "outline", onClick: () => handlePageChange(1), disabled: currentPage === 1, children: "First" }), _jsx(Button, { size: "small", variant: "outline", onClick: () => handlePageChange(currentPage - 1), disabled: currentPage === 1, children: "Prev" }), _jsx(Button, { size: "small", variant: "outline", onClick: () => handlePageChange(currentPage + 1), disabled: currentPage === totalPages, children: "Next" }), _jsx(Button, { size: "small", variant: "outline", onClick: () => handlePageChange(totalPages), disabled: currentPage === totalPages, children: "Last" })] })] })] }))] }));
}
//# sourceMappingURL=Table.js.map