/**
 * Trade Table
 *
 * Main table component for displaying trading data with filtering and sorting.
 */
import React from 'react';
import { CompleteTradeData, TradeFilters } from '../../types/trading';
import { TableColumn } from './TradeTableColumns';
export interface TradeTableProps {
    /** The trade data to display */
    data: CompleteTradeData[];
    /** Whether the table is loading */
    isLoading?: boolean;
    /** Whether the table has a border */
    bordered?: boolean;
    /** Whether the table has striped rows */
    striped?: boolean;
    /** Whether the table has hoverable rows */
    hoverable?: boolean;
    /** Whether the table is compact */
    compact?: boolean;
    /** Whether the table has a sticky header */
    stickyHeader?: boolean;
    /** The height of the table (e.g., '400px') */
    height?: string;
    /** Function called when a row is clicked */
    onRowClick?: (trade: CompleteTradeData, index: number) => void;
    /** Function to determine if a row is selected */
    isRowSelected?: (trade: CompleteTradeData, index: number) => boolean;
    /** Function called when a sort header is clicked */
    onSort?: (columnId: string, direction: 'asc' | 'desc') => void;
    /** The ID of the column to sort by */
    sortColumn?: string;
    /** The direction to sort */
    sortDirection?: 'asc' | 'desc';
    /** Whether the table has pagination */
    pagination?: boolean;
    /** The current page */
    currentPage?: number;
    /** The number of rows per page */
    pageSize?: number;
    /** The total number of rows */
    totalRows?: number;
    /** Function called when the page changes */
    onPageChange?: (page: number) => void;
    /** Function called when the page size changes */
    onPageSizeChange?: (pageSize: number) => void;
    /** Additional CSS class names */
    className?: string;
    /** Empty state message */
    emptyMessage?: string;
    /** Whether the table is scrollable horizontally */
    scrollable?: boolean;
    /** Whether to show filters */
    showFilters?: boolean;
    /** Current filter values */
    filters?: TradeFilters;
    /** Function called when filters change */
    onFiltersChange?: (filters: TradeFilters) => void;
    /** Column preset to use */
    columnPreset?: 'default' | 'compact' | 'performance';
    /** Custom columns (overrides preset) */
    customColumns?: TableColumn<CompleteTradeData>[];
    /** Whether rows are expandable */
    expandableRows?: boolean;
    /** Function to render expanded content */
    renderExpandedContent?: (trade: CompleteTradeData) => React.ReactNode;
}
/**
 * Trade Table Component
 */
export declare const TradeTable: React.FC<TradeTableProps>;
//# sourceMappingURL=TradeTable.d.ts.map