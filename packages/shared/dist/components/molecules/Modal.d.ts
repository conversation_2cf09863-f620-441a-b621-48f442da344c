/**
 * Modal Component
 *
 * A customizable modal component that follows the design system.
 */
import React from 'react';
export interface ModalProps {
    /** Whether the modal is open */
    isOpen: boolean;
    /** The title of the modal */
    title?: string;
    /** The content of the modal */
    children: React.ReactNode;
    /** Function called when the modal is closed */
    onClose: () => void;
    /** The size of the modal */
    size?: 'small' | 'medium' | 'large' | 'fullscreen';
    /** Whether the modal can be closed by clicking outside or pressing escape */
    closeOnOutsideClick?: boolean;
    /** Whether the modal has a close button */
    showCloseButton?: boolean;
    /** Custom footer content */
    footer?: React.ReactNode;
    /** Whether the modal has a footer */
    hasFooter?: boolean;
    /** Primary action button text */
    primaryActionText?: string;
    /** Function called when the primary action button is clicked */
    onPrimaryAction?: () => void;
    /** Whether the primary action button is disabled */
    primaryActionDisabled?: boolean;
    /** Whether the primary action button is loading */
    primaryActionLoading?: boolean;
    /** Secondary action button text */
    secondaryActionText?: string;
    /** Function called when the secondary action button is clicked */
    onSecondaryAction?: () => void;
    /** Whether the secondary action button is disabled */
    secondaryActionDisabled?: boolean;
    /** Additional CSS class names */
    className?: string;
    /** Z-index for the modal */
    zIndex?: number;
    /** Whether the modal is centered vertically */
    centered?: boolean;
    /** Whether the modal has a backdrop */
    hasBackdrop?: boolean;
    /** Whether the modal is scrollable */
    scrollable?: boolean;
}
/**
 * Modal Component
 *
 * A customizable modal component that follows the design system.
 */
export declare const Modal: React.FC<ModalProps>;
//# sourceMappingURL=Modal.d.ts.map