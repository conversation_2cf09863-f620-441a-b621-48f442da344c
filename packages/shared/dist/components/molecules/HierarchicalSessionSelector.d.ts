/**
 * Hierarchical Session Selector Component
 *
 * A sophisticated session selector that supports both broad sessions
 * and specific macro periods with intelligent grouping and validation.
 */
import React from 'react';
import { SessionSelection } from '../../types/tradingSessions';
export interface HierarchicalSessionSelectorProps {
    /** Current selection */
    value?: SessionSelection;
    /** Change handler */
    onChange: (selection: SessionSelection) => void;
    /** Whether to show macro periods */
    showMacroPeriods?: boolean;
    /** Whether to show current session indicator */
    showCurrentSession?: boolean;
    /** Whether to allow custom time ranges */
    allowCustomRange?: boolean;
    /** Placeholder text */
    placeholder?: string;
    /** Whether the selector is disabled */
    disabled?: boolean;
    /** Error message */
    error?: string;
    /** CSS class name */
    className?: string;
}
/**
 * Hierarchical Session Selector Component
 */
export declare const HierarchicalSessionSelector: React.FC<HierarchicalSessionSelectorProps>;
//# sourceMappingURL=HierarchicalSessionSelector.d.ts.map