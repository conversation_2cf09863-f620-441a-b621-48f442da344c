import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Hierarchical Session Selector Component
 *
 * A sophisticated session selector that supports both broad sessions
 * and specific macro periods with intelligent grouping and validation.
 */
import { useState, useMemo } from 'react';
import styled from 'styled-components';
import { useSessionSelection } from '../../hooks/useSessionSelection';
import { SessionUtils } from '../../utils/sessionUtils';
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;
const SelectorContainer = styled.div `
  position: relative;
  border: 1px solid
    ${({ theme, hasError }) => hasError ? theme.colors?.error || '#ef4444' : theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  transition: all 0.2s ease;
  opacity: ${({ disabled }) => (disabled ? 0.6 : 1)};
  pointer-events: ${({ disabled }) => (disabled ? 'none' : 'auto')};

  &:hover {
    border-color: ${({ theme, hasError }) => hasError ? theme.colors?.error || '#ef4444' : theme.colors?.primary || '#dc2626'}40;
  }

  &:focus-within {
    border-color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || '#dc2626'}20;
  }
`;
const SelectedValue = styled.div `
  padding: ${({ theme }) => theme.spacing?.md || '12px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;
const DropdownIcon = styled.div `
  transition: transform 0.2s ease;
  transform: ${({ isOpen }) => (isOpen ? 'rotate(180deg)' : 'rotate(0deg)')};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
`;
const DropdownMenu = styled.div `
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
  max-height: 400px;
  overflow-y: auto;
  display: ${({ isOpen }) => (isOpen ? 'block' : 'none')};
`;
const MultiSessionGroup = styled.div `
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  background: ${({ theme }) => theme.colors?.background || '#111827'};

  &:last-child {
    border-bottom: none;
  }
`;
const MultiSessionHeader = styled.div `
  padding: ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme, isSelected }) => isSelected ? theme.colors?.primary || '#dc2626' : theme.colors?.surface || '#1f2937'};
  color: ${({ theme, isSelected }) => isSelected ? '#ffffff' : theme.colors?.textPrimary || '#ffffff'};
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.2s ease;
  border-left: 3px solid ${({ theme }) => theme.colors?.warning || '#f59e0b'};

  &:hover {
    background: ${({ theme, isSelected }) => isSelected ? theme.colors?.primary || '#dc2626' : theme.colors?.border || '#4b5563'}40;
  }
`;
const MultiSessionIndicator = styled.div `
  display: inline-flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.warning || '#f59e0b'};
  font-weight: 500;
`;
const SessionGroup = styled.div `
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};

  &:last-child {
    border-bottom: none;
  }
`;
const SessionHeader = styled.div `
  padding: ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme, isSelected }) => isSelected ? theme.colors?.primary || '#dc2626' : 'transparent'};
  color: ${({ theme, isSelected }) => isSelected ? '#ffffff' : theme.colors?.textPrimary || '#ffffff'};
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.2s ease;

  &:hover {
    background: ${({ theme, isSelected }) => isSelected ? theme.colors?.primary || '#dc2626' : theme.colors?.border || '#4b5563'}40;
  }
`;
const MacroList = styled.div `
  background: ${({ theme }) => theme.colors?.background || '#111827'};
`;
const MacroItem = styled.div `
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.lg || '24px'};
  color: ${({ theme, isSelected }) => isSelected ? theme.colors?.primary || '#dc2626' : theme.colors?.textSecondary || '#9ca3af'};
  cursor: pointer;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  transition: all 0.2s ease;
  border-left: 3px solid
    ${({ theme, isSelected }) => (isSelected ? theme.colors?.primary || '#dc2626' : 'transparent')};

  &:hover {
    background: ${({ theme }) => theme.colors?.border || '#4b5563'}20;
    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  }
`;
const CurrentSessionIndicator = styled.div `
  display: inline-flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.success || '#10b981'};
  font-weight: 500;
`;
const ErrorMessage = styled.div `
  color: ${({ theme }) => theme.colors?.error || '#ef4444'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};
`;
/**
 * Hierarchical Session Selector Component
 */
export const HierarchicalSessionSelector = ({ value, onChange, showMacroPeriods = true, showCurrentSession = true, allowCustomRange = false, placeholder = 'Select session or macro period', disabled = false, error, className, }) => {
    const [isOpen, setIsOpen] = useState(false);
    const { hierarchicalOptions, currentSession, isCurrentSessionActive, selectSession, selectMacro, } = useSessionSelection({
        onSelectionChange: onChange,
    });
    // Get multi-session macros
    const multiSessionMacros = useMemo(() => {
        return SessionUtils.getMultiSessionMacros();
    }, []);
    // Display value
    const displayValue = useMemo(() => {
        if (value?.displayLabel) {
            return value.displayLabel;
        }
        return placeholder;
    }, [value, placeholder]);
    // Handle session selection
    const handleSessionSelect = (sessionType) => {
        selectSession(sessionType);
        setIsOpen(false);
    };
    // Handle macro selection
    const handleMacroSelect = (macroType) => {
        selectMacro(macroType);
        setIsOpen(false);
    };
    // Check if session is selected
    const isSessionSelected = (sessionType) => {
        return value?.session === sessionType && value?.selectionType === 'session';
    };
    // Check if macro is selected
    const isMacroSelected = (macroType) => {
        return value?.macroPeriod === macroType && value?.selectionType === 'macro';
    };
    // Check if session is current
    const isCurrentSession = (sessionType) => {
        return currentSession?.session === sessionType;
    };
    return (_jsxs(Container, { className: className, hasError: !!error, children: [_jsxs(SelectorContainer, { hasError: !!error, disabled: disabled, onClick: () => !disabled && setIsOpen(!isOpen), children: [_jsxs(SelectedValue, { children: [_jsx("span", { children: displayValue }), _jsx(DropdownIcon, { isOpen: isOpen, children: "\u25BC" })] }), _jsxs(DropdownMenu, { isOpen: isOpen, children: [showMacroPeriods && multiSessionMacros.length > 0 && (_jsx(MultiSessionGroup, { children: multiSessionMacros.map((macro) => (_jsxs(MultiSessionHeader, { isSelected: isMacroSelected(macro.type), onClick: (e) => {
                                        e.stopPropagation();
                                        handleMacroSelect(macro.type);
                                    }, children: [_jsx("span", { children: macro.name }), _jsx(MultiSessionIndicator, { children: "\uD83C\uDF10 MULTI-SESSION" })] }, macro.type))) })), hierarchicalOptions.map(({ session, sessionLabel, macros }) => (_jsxs(SessionGroup, { children: [_jsxs(SessionHeader, { isSelected: isSessionSelected(session), onClick: (e) => {
                                            e.stopPropagation();
                                            handleSessionSelect(session);
                                        }, children: [_jsx("span", { children: sessionLabel }), showCurrentSession && isCurrentSession(session) && (_jsx(CurrentSessionIndicator, { children: "\uD83D\uDD34 LIVE" }))] }), showMacroPeriods && macros.length > 0 && (_jsx(MacroList, { children: macros.map(({ value: macroValue, label }) => (_jsxs(MacroItem, { isSelected: isMacroSelected(macroValue), onClick: (e) => {
                                                e.stopPropagation();
                                                handleMacroSelect(macroValue);
                                            }, children: [label, SessionUtils.hasSubPeriods(macroValue) && (_jsx("span", { style: { marginLeft: '8px', fontSize: '0.75rem', opacity: 0.7 }, children: "\uD83D\uDCCB Has sub-periods" }))] }, macroValue))) }))] }, session)))] })] }), error && _jsx(ErrorMessage, { children: error })] }));
};
//# sourceMappingURL=HierarchicalSessionSelector.js.map