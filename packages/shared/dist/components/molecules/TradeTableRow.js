import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
/**
 * Trade Table Row
 *
 * Individual row component for trading data tables.
 */
import { useState } from 'react';
import styled, { css } from 'styled-components';
const TableRow = styled.tr `
  ${({ striped, theme, isSelected }) => striped &&
    !isSelected &&
    css `
      &:nth-child(even) {
        background-color: ${theme.colors?.background || '#f8f9fa'}50;
      }
    `}

  ${({ hoverable, theme, isSelected }) => hoverable &&
    !isSelected &&
    css `
      &:hover {
        background-color: ${theme.colors?.background || '#f8f9fa'}aa;
      }
    `}

  ${({ isSelected, theme }) => isSelected &&
    css `
      background-color: ${theme.colors?.primary || '#3b82f6'}15;
    `}

  ${({ isClickable }) => isClickable &&
    css `
      cursor: pointer;
    `}

  ${({ isExpanded, theme }) => isExpanded &&
    css `
      border-bottom: 2px solid ${theme.colors?.primary || '#3b82f6'};
    `}
`;
const TableCell = styled.td `
  text-align: ${({ align }) => align || 'left'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e5e7eb'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#111827'};
  padding: ${({ theme }) => theme.spacing?.sm || '12px'}
    ${({ theme }) => theme.spacing?.md || '16px'};
  vertical-align: middle;
`;
const ExpandedRow = styled.tr `
  display: ${({ isVisible }) => (isVisible ? 'table-row' : 'none')};
`;
const ExpandedCell = styled.td `
  padding: 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e5e7eb'};
`;
const ExpandedContent = styled.div `
  padding: ${({ theme }) => theme.spacing?.md || '16px'};
  background-color: ${({ theme }) => theme.colors?.background || '#f8f9fa'}30;
  border-left: 3px solid ${({ theme }) => theme.colors?.primary || '#3b82f6'};
`;
const ExpandButton = styled.button `
  background: none;
  border: none;
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing?.xs || '8px'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors?.background || '#f8f9fa'};
    color: ${({ theme }) => theme.colors?.primary || '#3b82f6'};
  }

  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors?.primary || '#3b82f6'};
    outline-offset: 2px;
  }
`;
const ExpandIcon = styled.span `
  display: inline-block;
  transition: transform 0.2s ease;
  transform: ${({ isExpanded }) => (isExpanded ? 'rotate(90deg)' : 'rotate(0deg)')};

  &::after {
    content: '▶';
  }
`;
const TradeDetails = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
`;
const DetailGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '8px'};
`;
const DetailLabel = styled.span `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
  color: ${({ theme }) => theme.colors?.textSecondary || '#6b7280'};
`;
const DetailValue = styled.span `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#111827'};
`;
/**
 * Default expanded content for trade rows
 */
const DefaultExpandedContent = ({ trade }) => (_jsxs(TradeDetails, { children: [trade.fvg_details && (_jsxs(DetailGroup, { children: [_jsx(DetailLabel, { children: "FVG Details" }), _jsxs(DetailValue, { children: ["Type: ", trade.fvg_details.rd_type || '-'] }), _jsxs(DetailValue, { children: ["Entry Version: ", trade.fvg_details.entry_version || '-'] }), _jsxs(DetailValue, { children: ["Draw on Liquidity: ", trade.fvg_details.draw_on_liquidity || '-'] })] })), trade.setup && (_jsxs(DetailGroup, { children: [_jsx(DetailLabel, { children: "Setup Classification" }), _jsxs(DetailValue, { children: ["Primary: ", trade.setup.primary_setup || '-'] }), _jsxs(DetailValue, { children: ["Secondary: ", trade.setup.secondary_setup || '-'] }), _jsxs(DetailValue, { children: ["Liquidity: ", trade.setup.liquidity_taken || '-'] })] })), trade.analysis && (_jsxs(DetailGroup, { children: [_jsx(DetailLabel, { children: "Analysis" }), _jsxs(DetailValue, { children: ["DOL Target: ", trade.analysis.dol_target_type || '-'] }), _jsxs(DetailValue, { children: ["Path Quality: ", trade.analysis.path_quality || '-'] }), _jsxs(DetailValue, { children: ["Clustering: ", trade.analysis.clustering || '-'] })] })), _jsxs(DetailGroup, { children: [_jsx(DetailLabel, { children: "Timing" }), _jsxs(DetailValue, { children: ["Entry: ", trade.trade.entry_time || '-'] }), _jsxs(DetailValue, { children: ["Exit: ", trade.trade.exit_time || '-'] }), _jsxs(DetailValue, { children: ["FVG: ", trade.trade.fvg_time || '-'] }), _jsxs(DetailValue, { children: ["RD: ", trade.trade.rd_time || '-'] })] }), trade.trade.notes && (_jsxs(DetailGroup, { style: { gridColumn: '1 / -1' }, children: [_jsx(DetailLabel, { children: "Notes" }), _jsx(DetailValue, { children: trade.trade.notes })] }))] }));
/**
 * Trade Table Row Component
 */
export const TradeTableRow = ({ trade, index, columns, isSelected = false, hoverable = true, striped = true, expandable = false, isExpanded = false, onRowClick, onToggleExpand, expandedContent, }) => {
    const [localExpanded, setLocalExpanded] = useState(false);
    const expanded = isExpanded !== undefined ? isExpanded : localExpanded;
    const handleRowClick = (e) => {
        // Don't trigger row click if clicking on expand button
        if (e.target.closest('button')) {
            return;
        }
        onRowClick?.(trade, index);
    };
    const handleToggleExpand = (e) => {
        e.stopPropagation();
        if (onToggleExpand) {
            onToggleExpand(trade, index);
        }
        else {
            setLocalExpanded(!localExpanded);
        }
    };
    // Filter out hidden columns
    const visibleColumns = columns.filter((col) => !col.hidden);
    return (_jsxs(_Fragment, { children: [_jsxs(TableRow, { hoverable: hoverable, striped: striped, isSelected: isSelected, isClickable: !!onRowClick, isExpanded: expanded, onClick: handleRowClick, children: [expandable && (_jsx(TableCell, { align: "center", style: { width: '40px', padding: '8px' }, children: _jsx(ExpandButton, { onClick: handleToggleExpand, children: _jsx(ExpandIcon, { isExpanded: expanded }) }) })), visibleColumns.map((column) => (_jsx(TableCell, { align: column.align, children: column.cell(trade, index) }, column.id)))] }), expandable && (_jsx(ExpandedRow, { isVisible: expanded, children: _jsx(ExpandedCell, { colSpan: visibleColumns.length + 1, children: _jsx(ExpandedContent, { children: expandedContent || _jsx(DefaultExpandedContent, { trade: trade }) }) }) }))] }));
};
//# sourceMappingURL=TradeTableRow.js.map