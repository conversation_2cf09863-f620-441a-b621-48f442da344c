/**
 * Trade Table Filters
 *
 * Filtering controls for trading data tables.
 */
import React from 'react';
import { TradeFilters } from '../../types/trading';
export interface TradeTableFiltersProps {
    /** Current filter values */
    filters: TradeFilters;
    /** Function called when filters change */
    onFiltersChange: (filters: TradeFilters) => void;
    /** Function called when filters are reset */
    onReset?: () => void;
    /** Whether the filters are in a loading state */
    isLoading?: boolean;
    /** Whether to show advanced filters */
    showAdvanced?: boolean;
    /** Function to toggle advanced filters */
    onToggleAdvanced?: () => void;
}
/**
 * Trade Table Filters Component
 */
export declare const TradeTableFilters: React.FC<TradeTableFiltersProps>;
//# sourceMappingURL=TradeTableFilters.d.ts.map