import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled, { css } from 'styled-components';
import { useSortableTable } from '../../hooks/useSortableTable';
const Container = styled.div `
  overflow-x: auto;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
`;
const Table = styled.table `
  width: 100%;
  border-collapse: collapse;
  font-size: ${({ theme, $size }) => {
    switch ($size) {
        case 'sm':
            return theme.fontSizes?.xs || '12px';
        case 'lg':
            return theme.fontSizes?.md || '16px';
        default:
            return theme.fontSizes?.sm || '14px';
    }
}};
`;
const TableHead = styled.thead `
  background-color: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border-bottom: 2px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
`;
const TableBody = styled.tbody ``;
const TableRow = styled.tr `
  ${({ $striped, theme }) => $striped &&
    css `
      &:nth-child(even) {
        background-color: ${theme.colors?.background || '#0f0f0f'};
      }
    `}

  ${({ $hoverable, theme }) => $hoverable &&
    css `
      &:hover {
        background-color: ${theme.colors?.surface || '#1f2937'};
      }
    `}

  ${({ $clickable }) => $clickable &&
    css `
      cursor: pointer;
    `}

  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
`;
const TableHeaderCell = styled.th `
  text-align: left;
  font-weight: ${({ theme }) => theme.fontWeights?.semibold || '600'};
  color: ${({ theme, $active }) => $active
    ? theme.colors?.primary || '#dc2626'
    : theme.colors?.textPrimary || '#ffffff'};
  cursor: ${({ $sortable }) => ($sortable ? 'pointer' : 'default')};
  user-select: none;
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};

  padding: ${({ theme, $size }) => {
    switch ($size) {
        case 'sm':
            return `${theme.spacing?.xs || '4px'} ${theme.spacing?.sm || '8px'}`;
        case 'lg':
            return `${theme.spacing?.md || '12px'} ${theme.spacing?.lg || '16px'}`;
        default:
            return `${theme.spacing?.sm || '8px'} ${theme.spacing?.md || '12px'}`;
    }
}};

  &:hover {
    ${({ $sortable, theme }) => $sortable &&
    css `
        color: ${theme.colors?.primary || '#dc2626'};
      `}
  }

  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors?.primary || '#dc2626'};
    outline-offset: -2px;
  }
`;
const TableCell = styled.td `
  padding: ${({ theme, $size }) => {
    switch ($size) {
        case 'sm':
            return `${theme.spacing?.xs || '4px'} ${theme.spacing?.sm || '8px'}`;
        case 'lg':
            return `${theme.spacing?.md || '12px'} ${theme.spacing?.lg || '16px'}`;
        default:
            return `${theme.spacing?.sm || '8px'} ${theme.spacing?.md || '12px'}`;
    }
}};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
`;
const SortIcon = styled.span `
  display: inline-block;
  margin-left: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
  
  &::after {
    content: '${({ $direction }) => ($direction === 'asc' ? '↑' : '↓')}';
  }
`;
const EmptyState = styled.div `
  padding: ${({ theme, $size }) => {
    switch ($size) {
        case 'sm':
            return theme.spacing?.md || '12px';
        case 'lg':
            return theme.spacing?.xl || '24px';
        default:
            return theme.spacing?.lg || '16px';
    }
}};
  text-align: center;
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  font-style: italic;
`;
/**
 * SortableTable Component
 *
 * A reusable sortable table component with consistent styling and behavior.
 *
 * @example
 * ```typescript
 * const columns: SortableColumn<Trade>[] = [
 *   { field: 'symbol', label: 'Symbol', sortable: true },
 *   { field: 'profitLoss', label: 'P&L', sortable: true },
 *   { field: 'date', label: 'Date', sortable: true },
 * ];
 *
 * <SortableTable
 *   data={trades}
 *   columns={columns}
 *   defaultSort={{ field: 'profitLoss', direction: 'desc' }}
 *   onRowClick={(trade) => console.log('Clicked trade:', trade)}
 *   renderCell={(value, row, column) => {
 *     if (column.field === 'profitLoss') {
 *       return <ProfitLossCell amount={value} />;
 *     }
 *     return value;
 *   }}
 * />
 * ```
 */
export const SortableTable = ({ data, columns, className, emptyMessage = 'No data available', defaultSort, renderCell, onRowClick, size = 'md', striped = true, hoverable = true, }) => {
    const { sortedData, handleSort, getSortIcon, isSorted, } = useSortableTable({
        data,
        columns,
        defaultSort,
    });
    if (data.length === 0) {
        return (_jsx(Container, { className: className, children: _jsx(EmptyState, { "$size": size, children: emptyMessage }) }));
    }
    return (_jsx(Container, { className: className, children: _jsxs(Table, { "$size": size, "$striped": striped, "$hoverable": hoverable, children: [_jsx(TableHead, { children: _jsx(TableRow, { "$striped": false, "$hoverable": false, "$clickable": false, children: columns.map((column) => (_jsxs(TableHeaderCell, { "$sortable": column.sortable || false, "$active": isSorted(column.field), "$size": size, onClick: () => column.sortable && handleSort(column.field), tabIndex: column.sortable ? 0 : -1, onKeyDown: (e) => {
                                if (column.sortable && (e.key === 'Enter' || e.key === ' ')) {
                                    e.preventDefault();
                                    handleSort(column.field);
                                }
                            }, role: column.sortable ? 'button' : undefined, "aria-sort": isSorted(column.field)
                                ? getSortIcon(column.field) === '↑'
                                    ? 'ascending'
                                    : 'descending'
                                : undefined, children: [column.label, isSorted(column.field) && (_jsx(SortIcon, { "$direction": getSortIcon(column.field) === '↑' ? 'asc' : 'desc' }))] }, String(column.field)))) }) }), _jsx(TableBody, { children: sortedData.map((row, index) => (_jsx(TableRow, { "$striped": striped, "$hoverable": hoverable, "$clickable": !!onRowClick, onClick: () => onRowClick?.(row, index), tabIndex: onRowClick ? 0 : -1, onKeyDown: (e) => {
                            if (onRowClick && (e.key === 'Enter' || e.key === ' ')) {
                                e.preventDefault();
                                onRowClick(row, index);
                            }
                        }, role: onRowClick ? 'button' : undefined, children: columns.map((column) => {
                            const value = row[column.field];
                            return (_jsx(TableCell, { "$size": size, children: renderCell ? renderCell(value, row, column) : String(value) }, String(column.field)));
                        }) }, index))) })] }) }));
};
export default SortableTable;
//# sourceMappingURL=SortableTable.js.map