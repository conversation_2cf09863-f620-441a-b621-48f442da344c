/**
 * F1 Official Theme
 *
 * Authentic F1 app theme with official F1 colors and timing screen aesthetics.
 * Matches CSS variables in [data-theme='f1-official'].
 */

import {
  spacing,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  breakpoints,
  borderRadius,
  shadows,
  transitions,
  zIndex,
} from './tokens';
import { Theme } from './types';

/**
 * F1 Official Theme
 *
 * Authentic F1 timing screen theme with official F1 red and gold colors.
 * Designed to match the official F1 app aesthetic.
 */
export const f1OfficialTheme: Theme = {
  name: 'f1-official',
  colors: {
    // F1 Brand Colors - Official Red Primary
    primary: '#e10600',
    primaryDark: '#b30500',
    primaryLight: '#ff1e1e',

    // F1 Secondary Colors - Deep Navy
    secondary: '#15151e',
    secondaryDark: '#0f0f17',
    secondaryLight: '#1e1e2e',

    // F1 Accent Colors - Championship Gold
    accent: '#ffd700',
    accentDark: '#e6be1d',
    accentLight: '#ffdc4a',

    // F1 Status Colors - Timing Screen Colors
    success: '#00ff41', // F1 timing green (sector improvements)
    warning: '#ffd700', // F1 yellow flags
    error: '#ff1e1e', // F1 timing red (sector losses)
    danger: '#ff1e1e', // F1 danger red (same as error for F1 theme)
    info: '#00b4d8', // F1 information blue

    // Background Colors - F1 App Style
    background: '#15151e',
    surface: '#1e1e2e',
    cardBackground: '#2a2a3a',
    elevated: '#353545',

    // Border Colors
    border: '#3a3a4a',
    divider: '#4a4a5a',

    // Text Colors - High Contrast F1 Style
    textPrimary: '#ffffff',
    textSecondary: '#b8b8c8',
    textDisabled: '#8b8b9b',
    textInverse: '#15151e',

    // Chart Colors
    chartGrid: 'rgba(255, 255, 255, 0.1)',
    chartLine: '#e10600',
    chartAxis: '#b8b8c8',
    chartTooltip: 'rgba(42, 42, 58, 0.9)',

    // Trading Colors
    profit: '#00ff41', // F1 timing green
    loss: '#ff1e1e', // F1 timing red
    neutral: '#b8b8c8',

    // Tab Colors
    tabActive: '#e10600',
    tabInactive: '#8b8b9b',

    // Component Colors
    tooltipBackground: 'rgba(42, 42, 58, 0.9)',
    modalBackground: 'rgba(21, 21, 30, 0.8)',
    sidebarBackground: '#1e1e2e',
    headerBackground: 'rgba(21, 21, 30, 0.9)',

    // F1 Session States
    sessionActive: '#e10600', // F1 red for active sessions
    sessionOptimal: '#ffd700', // Gold for optimal windows
    sessionCaution: '#ff8700', // Orange for caution periods
    sessionTransition: '#00b4d8', // Blue for transitions
    sessionInactive: '#8b8b9b', // Muted for inactive

    // F1 Performance States
    performanceExcellent: '#00ff41', // Timing green
    performanceGood: '#ffd700', // Championship gold
    performanceAverage: '#ff8700', // Warning orange
    performancePoor: '#ff1e1e', // Timing red
    performanceAvoid: '#8b8b9b', // Muted gray
  },
  spacing,
  breakpoints,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  borderRadius,
  shadows,
  transitions,
  zIndex,
};

export default f1OfficialTheme;
