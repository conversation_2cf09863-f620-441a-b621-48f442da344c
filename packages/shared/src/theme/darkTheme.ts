/**
 * Dark Theme
 *
 * This file contains the dark theme for the ADHD Trading Dashboard.
 */

import { Theme } from './types';
import {
  baseColors,
  darkModeColors,
  spacing,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  breakpoints,
  borderRadius,
  shadows,
  transitions,
  zIndex,
} from './tokens';

/**
 * Mercedes Dark Theme
 *
 * Mercedes-inspired dark theme with silver accents and sophisticated contrast.
 * Professional Mercedes F1 pit wall aesthetic for extended use.
 */
export const darkTheme: Theme = {
  name: 'mercedes-dark',
  colors: {
    // Mercedes-inspired primary colors (Silver/Teal accents)
    primary: baseColors.f1Silver,
    primaryDark: baseColors.gray500,
    primaryLight: baseColors.gray300,

    // Secondary colors - Subtle Mercedes teal
    secondary: baseColors.f1MercedesGreenDark,
    secondaryDark: '#006B5D',
    secondaryLight: baseColors.f1MercedesGreen,

    // Accent colors - Mercedes silver
    accent: baseColors.f1Silver,
    accentDark: baseColors.gray500,
    accentLight: baseColors.gray300,

    // Mercedes-themed status colors
    success: baseColors.f1MercedesGreen, // Mercedes green for success
    warning: baseColors.f1Silver, // Silver for warnings
    error: baseColors.red, // Keep red for errors
    danger: baseColors.red, // Keep red for danger
    info: baseColors.f1MercedesGreenDark, // Dark teal for info

    // Neutral colors
    background: baseColors.gray900, // Slightly different from F1 theme
    surface: baseColors.gray800,
    elevated: baseColors.gray700, // Added elevated color for dark theme
    cardBackground: baseColors.gray800,
    border: baseColors.gray700,
    divider: 'rgba(255, 255, 255, 0.1)',

    // Text colors - Improved contrast for dark theme
    textPrimary: baseColors.white, // #ffffff - High contrast
    textSecondary: baseColors.gray200, // #e5e7eb - Better contrast than gray300
    textDisabled: baseColors.gray400, // #9ca3af - More visible than gray500
    textInverse: baseColors.gray900,

    // Chart colors
    chartGrid: darkModeColors.chartGrid,
    chartLine: baseColors.f1Blue, // Using blue instead of red
    chartAxis: baseColors.gray400,
    chartTooltip: darkModeColors.tooltipBackground,

    // Mercedes-themed trading colors
    profit: baseColors.f1MercedesGreen, // Mercedes green for profits
    loss: baseColors.red, // Keep red for losses
    neutral: baseColors.f1Silver, // Mercedes silver for neutral

    // Mercedes tab colors
    tabActive: baseColors.f1Silver, // Silver for active tabs
    tabInactive: baseColors.gray600,

    // Component specific colors
    tooltipBackground: 'rgba(26, 32, 44, 0.9)', // Slightly different from F1 theme
    modalBackground: 'rgba(26, 32, 44, 0.8)',
    sidebarBackground: baseColors.gray900,
    headerBackground: 'rgba(0, 0, 0, 0.3)',

    // Mercedes-themed session states
    sessionActive: baseColors.f1MercedesGreen, // Mercedes green for active
    sessionOptimal: baseColors.f1MercedesGreenLight, // Bright Mercedes green
    sessionCaution: baseColors.f1Silver, // Silver for caution
    sessionTransition: baseColors.gray300, // Light gray for transitions
    sessionInactive: baseColors.gray600, // Gray for inactive

    // Mercedes performance states
    performanceExcellent: baseColors.f1MercedesGreen,
    performanceGood: baseColors.f1Silver,
    performanceAverage: baseColors.gray400,
    performancePoor: baseColors.gray500,
    performanceAvoid: baseColors.red,
  },
  spacing,
  breakpoints,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  borderRadius,
  shadows,
  transitions,
  zIndex,
};
