/**
 * Light Theme
 *
 * This file contains the light theme for the ADHD Trading Dashboard.
 */

import { Theme } from './types';
import {
  baseColors,
  spacing,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  breakpoints,
  borderRadius,
  shadows,
  transitions,
  zIndex,
} from './tokens';

/**
 * F1 Official Theme
 *
 * Official F1 Driver Tracker theme with authentic F1 app aesthetics.
 * Deep charcoal background with F1 red branding and racing colors.
 */
export const f1OfficialTheme: Theme = {
  name: 'f1-official',
  colors: {
    // F1 Official Primary Colors
    primary: baseColors.f1Red, // Official F1 red
    primaryDark: baseColors.f1RedDark,
    primaryLight: baseColors.f1RedLight,

    // F1 Racing Blue for information
    secondary: baseColors.f1Blue,
    secondaryDark: baseColors.f1BlueDark,
    secondaryLight: baseColors.f1BlueLight,

    // F1 Yellow accent colors
    accent: '#FFD700', // F1 yellow flags
    accentDark: '#E6C200',
    accentLight: '#FFF700',

    // F1 Racing Status Colors (like live timing)
    success: '#00FF41', // Bright green like F1 timing gains
    warning: '#FFD700', // F1 yellow flags
    error: '#FF1E1E', // Bright red like F1 timing losses
    danger: '#FF1E1E', // F1 red flags
    info: '#00B4D8', // F1 blue information

    // F1 Official Background Colors (deep charcoal like driver tracker)
    background: '#15151E', // Deep F1 black
    surface: '#1E1E2E', // Slightly lighter panels
    elevated: '#2A2A3A', // Card backgrounds
    cardBackground: '#2A2A3A', // F1 data cards
    border: '#3A3A4A', // Subtle borders
    divider: 'rgba(255, 255, 255, 0.1)',

    // F1 Official Text Colors
    textPrimary: '#FFFFFF', // Primary white text
    textSecondary: '#B8B8C8', // Secondary gray text
    textDisabled: '#8B8B9B', // Muted text
    textInverse: '#15151E', // Inverse text for light backgrounds

    // F1 Official Chart Colors (like live timing displays)
    chartGrid: 'rgba(255, 255, 255, 0.1)',
    chartLine: baseColors.f1Red,
    chartAxis: '#B8B8C8',
    chartTooltip: 'rgba(21, 21, 30, 0.95)',

    // F1 Racing Trading Colors (like timing screens)
    profit: '#00FF41', // Bright green like F1 timing gains
    loss: '#FF1E1E', // Bright red like F1 timing losses
    neutral: '#FFD700', // F1 yellow for neutral

    // F1 Official Tab Colors
    tabActive: baseColors.f1Red, // F1 red for active tabs
    tabInactive: '#8B8B9B', // Muted gray for inactive

    // F1 Official Component Colors
    tooltipBackground: 'rgba(21, 21, 30, 0.95)',
    modalBackground: 'rgba(21, 21, 30, 0.9)',
    sidebarBackground: '#1A1A24', // F1 sidebar background
    headerBackground: 'rgba(21, 21, 30, 0.95)',

    // F1 Official Session States (like race status)
    sessionActive: '#00FF41', // Bright green like F1 timing
    sessionOptimal: '#9D4EDD', // Purple for fastest like F1
    sessionCaution: '#FFD700', // F1 yellow flags
    sessionTransition: '#00B4D8', // F1 blue for transitions
    sessionInactive: '#8B8B9B', // Muted gray

    // F1 Official Performance States (like driver performance)
    performanceExcellent: '#00FF41', // Bright green like fastest lap
    performanceGood: '#00B4D8', // F1 blue for good performance
    performanceAverage: '#FFD700', // F1 yellow for average
    performancePoor: '#FF8700', // Orange for poor performance
    performanceAvoid: '#FF1E1E', // Bright red for avoid
  },
  spacing,
  breakpoints,
  fontSizes,
  fontWeights,
  lineHeights,
  fontFamilies,
  borderRadius,
  shadows,
  transitions,
  zIndex,
};

// Backward compatibility export (lightTheme now points to F1 Official)
export const lightTheme = f1OfficialTheme;
