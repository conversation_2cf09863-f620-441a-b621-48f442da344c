/**
 * Trade Table Columns
 *
 * Column definitions for trading data tables using the new trading types.
 */
import React from 'react';
import styled from 'styled-components';
import { Badge } from '../atoms/Badge';
import { CompleteTradeData } from '../../types/trading';

// Type-safe field accessors for TradeRecord
// type TradeRecordField = keyof TradeRecord; // Unused for now
// type TradeTableField = TradeRecordField | 'symbol' | 'setup' | 'analysis'; // Unused for now

// Column ID constants to prevent typos
export const TRADE_COLUMN_IDS = {
  DATE: 'date' as const,
  SYMBOL: 'symbol' as const,
  DIRECTION: 'direction' as const,
  MODEL_TYPE: 'model_type' as const,
  SESSION: 'session' as const,
  ENTRY_PRICE: 'entry_price' as const,
  EXIT_PRICE: 'exit_price' as const,
  R_MULTIPLE: 'r_multiple' as const,
  ACHIEVED_PL: 'achieved_pl' as const,
  WIN_LOSS: 'win_loss' as const,
  PATTERN_QUALITY: 'pattern_quality_rating' as const,
  ENTRY_TIME: 'entry_time' as const,
  EXIT_TIME: 'exit_time' as const,
} as const;

// Re-export the column interface for backward compatibility
export interface TableColumn<T extends Record<string, any>> {
  /** Unique identifier for the column */
  id: string;
  /** Header text for the column */
  header: React.ReactNode;
  /** Function to render the cell content */
  cell: (row: T, rowIndex: number) => React.ReactNode;
  /** Whether the column is sortable */
  sortable?: boolean;
  /** Width of the column (e.g., '100px', '20%') */
  width?: string;
  /** Whether the column is hidden */
  hidden?: boolean;
  /** Alignment of the column content */
  align?: 'left' | 'center' | 'right';
}

// Styled components for trade-specific formatting
const ProfitLossCell = styled.span<{ isProfit: boolean }>`
  color: ${({ isProfit, theme }) =>
    isProfit ? theme.colors.success || '#10b981' : theme.colors.error || '#ef4444'};
  font-weight: ${({ theme }) => theme.fontWeights?.semibold || 600};
`;

const DirectionBadge = styled(Badge)<{ direction: 'Long' | 'Short' }>`
  background-color: ${({ direction, theme }) =>
    direction === 'Long' ? theme.colors.success || '#10b981' : theme.colors.error || '#ef4444'};
  color: white;
`;

const QualityRating = styled.span<{ rating: number }>`
  color: ${({ rating, theme }) => {
    if (rating >= 4) return theme.colors.success || '#10b981';
    if (rating >= 3) return theme.colors.warning || '#f59e0b';
    return theme.colors.error || '#ef4444';
  }};
  font-weight: ${({ theme }) => theme.fontWeights?.semibold || 600};
`;

const RMultipleCell = styled.span<{ rMultiple: number }>`
  color: ${({ rMultiple, theme }) =>
    rMultiple > 0 ? theme.colors.success || '#10b981' : theme.colors.error || '#ef4444'};
  font-weight: ${({ theme }) => theme.fontWeights?.semibold || 600};
`;

/**
 * Utility functions for formatting trade data
 */
const formatCurrencyLocal = (value: number | undefined): string => {
  if (value === undefined || value === null) return '-';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
  }).format(value);
};

// const formatPercentageLocal = (value: number | undefined): string => {
//   if (value === undefined || value === null) return '-';
//   return `${(value * 100).toFixed(2)}%`;
// }; // Unused for now

const formatDateLocal = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  } catch {
    return dateString;
  }
};

export const formatTime = (timeString: string | undefined): string => {
  if (!timeString) return '-';
  return timeString;
};

/**
 * Default column definitions for trade tables
 */
export const getTradeTableColumns = (): TableColumn<CompleteTradeData>[] => [
  {
    id: TRADE_COLUMN_IDS.DATE,
    header: 'Date',
    sortable: true,
    width: '100px',
    cell: (row) => formatDateLocal(row.trade[TRADE_COLUMN_IDS.DATE]),
  },
  {
    id: TRADE_COLUMN_IDS.SYMBOL,
    header: 'Symbol',
    sortable: true,
    width: '80px',
    cell: (row) => row.trade.market || 'MNQ',
  },
  {
    id: TRADE_COLUMN_IDS.DIRECTION,
    header: 'Direction',
    sortable: true,
    width: '80px',
    align: 'center',
    cell: (row) => (
      <DirectionBadge direction={row.trade[TRADE_COLUMN_IDS.DIRECTION]} size="small">
        {row.trade[TRADE_COLUMN_IDS.DIRECTION]}
      </DirectionBadge>
    ),
  },
  {
    id: TRADE_COLUMN_IDS.MODEL_TYPE,
    header: 'Model',
    sortable: true,
    width: '120px',
    cell: (row) => row.trade[TRADE_COLUMN_IDS.MODEL_TYPE] || '-',
  },
  {
    id: TRADE_COLUMN_IDS.SESSION,
    header: 'Session',
    sortable: true,
    width: '120px',
    cell: (row) => row.trade[TRADE_COLUMN_IDS.SESSION] || '-',
  },
  {
    id: TRADE_COLUMN_IDS.ENTRY_PRICE,
    header: 'Entry',
    sortable: true,
    width: '100px',
    align: 'right',
    cell: (row) => formatCurrencyLocal(row.trade[TRADE_COLUMN_IDS.ENTRY_PRICE]),
  },
  {
    id: TRADE_COLUMN_IDS.EXIT_PRICE,
    header: 'Exit',
    sortable: true,
    width: '100px',
    align: 'right',
    cell: (row) => formatCurrencyLocal(row.trade[TRADE_COLUMN_IDS.EXIT_PRICE]),
  },
  {
    id: TRADE_COLUMN_IDS.R_MULTIPLE,
    header: 'R Multiple',
    sortable: true,
    width: '100px',
    align: 'right',
    cell: (row) => (
      <RMultipleCell rMultiple={row.trade[TRADE_COLUMN_IDS.R_MULTIPLE] || 0}>
        {row.trade[TRADE_COLUMN_IDS.R_MULTIPLE]
          ? `${row.trade[TRADE_COLUMN_IDS.R_MULTIPLE]?.toFixed(2)}R`
          : '-'}
      </RMultipleCell>
    ),
  },
  {
    id: TRADE_COLUMN_IDS.ACHIEVED_PL,
    header: 'P&L',
    sortable: true,
    width: '100px',
    align: 'right',
    cell: (row) => (
      <ProfitLossCell isProfit={(row.trade[TRADE_COLUMN_IDS.ACHIEVED_PL] || 0) > 0}>
        {formatCurrencyLocal(row.trade[TRADE_COLUMN_IDS.ACHIEVED_PL])}
      </ProfitLossCell>
    ),
  },
  {
    id: TRADE_COLUMN_IDS.WIN_LOSS,
    header: 'Result',
    sortable: true,
    width: '80px',
    align: 'center',
    cell: (row) => (
      <Badge
        variant={row.trade[TRADE_COLUMN_IDS.WIN_LOSS] === 'Win' ? 'success' : 'error'}
        size="small"
      >
        {row.trade[TRADE_COLUMN_IDS.WIN_LOSS] || '-'}
      </Badge>
    ),
  },
  {
    id: TRADE_COLUMN_IDS.PATTERN_QUALITY,
    header: 'Quality',
    sortable: true,
    width: '80px',
    align: 'center',
    cell: (row) => (
      <QualityRating rating={row.trade[TRADE_COLUMN_IDS.PATTERN_QUALITY] || 0}>
        {row.trade[TRADE_COLUMN_IDS.PATTERN_QUALITY]
          ? `${row.trade[TRADE_COLUMN_IDS.PATTERN_QUALITY]}/5`
          : '-'}
      </QualityRating>
    ),
  },
  {
    id: TRADE_COLUMN_IDS.ENTRY_TIME,
    header: 'Entry Time',
    sortable: true,
    width: '100px',
    align: 'center',
    cell: (row) => formatTime(row.trade[TRADE_COLUMN_IDS.ENTRY_TIME]),
  },
  {
    id: TRADE_COLUMN_IDS.EXIT_TIME,
    header: 'Exit Time',
    sortable: true,
    width: '100px',
    align: 'center',
    cell: (row) => formatTime(row.trade[TRADE_COLUMN_IDS.EXIT_TIME]),
  },
];

/**
 * Compact column definitions for smaller displays
 */
export const getCompactTradeTableColumns = (): TableColumn<CompleteTradeData>[] => [
  {
    id: TRADE_COLUMN_IDS.DATE,
    header: 'Date',
    sortable: true,
    width: '90px',
    cell: (row) => formatDateLocal(row.trade[TRADE_COLUMN_IDS.DATE]),
  },
  {
    id: TRADE_COLUMN_IDS.SYMBOL,
    header: 'Symbol',
    sortable: true,
    width: '60px',
    cell: (row) => row.trade.market || 'MNQ',
  },
  {
    id: TRADE_COLUMN_IDS.DIRECTION,
    header: 'Dir',
    sortable: true,
    width: '50px',
    align: 'center',
    cell: (row) => (
      <DirectionBadge direction={row.trade[TRADE_COLUMN_IDS.DIRECTION]} size="small">
        {row.trade[TRADE_COLUMN_IDS.DIRECTION].charAt(0)}
      </DirectionBadge>
    ),
  },
  {
    id: TRADE_COLUMN_IDS.R_MULTIPLE,
    header: 'R',
    sortable: true,
    width: '60px',
    align: 'right',
    cell: (row) => (
      <RMultipleCell rMultiple={row.trade[TRADE_COLUMN_IDS.R_MULTIPLE] || 0}>
        {row.trade[TRADE_COLUMN_IDS.R_MULTIPLE]
          ? `${row.trade[TRADE_COLUMN_IDS.R_MULTIPLE]?.toFixed(1)}R`
          : '-'}
      </RMultipleCell>
    ),
  },
  {
    id: TRADE_COLUMN_IDS.ACHIEVED_PL,
    header: 'P&L',
    sortable: true,
    width: '80px',
    align: 'right',
    cell: (row) => (
      <ProfitLossCell isProfit={(row.trade[TRADE_COLUMN_IDS.ACHIEVED_PL] || 0) > 0}>
        {formatCurrencyLocal(row.trade[TRADE_COLUMN_IDS.ACHIEVED_PL])}
      </ProfitLossCell>
    ),
  },
  {
    id: TRADE_COLUMN_IDS.WIN_LOSS,
    header: 'Result',
    sortable: true,
    width: '60px',
    align: 'center',
    cell: (row) => (
      <Badge
        variant={row.trade[TRADE_COLUMN_IDS.WIN_LOSS] === 'Win' ? 'success' : 'error'}
        size="small"
      >
        {row.trade[TRADE_COLUMN_IDS.WIN_LOSS] === 'Win'
          ? 'W'
          : row.trade[TRADE_COLUMN_IDS.WIN_LOSS] === 'Loss'
          ? 'L'
          : '-'}
      </Badge>
    ),
  },
];

/**
 * Performance-focused column definitions
 */
export const getPerformanceTradeTableColumns = (): TableColumn<CompleteTradeData>[] => [
  {
    id: TRADE_COLUMN_IDS.DATE,
    header: 'Date',
    sortable: true,
    width: '100px',
    cell: (row) => formatDateLocal(row.trade[TRADE_COLUMN_IDS.DATE]),
  },
  {
    id: TRADE_COLUMN_IDS.MODEL_TYPE,
    header: 'Model',
    sortable: true,
    width: '120px',
    cell: (row) => row.trade[TRADE_COLUMN_IDS.MODEL_TYPE] || '-',
  },
  {
    id: TRADE_COLUMN_IDS.SESSION,
    header: 'Session',
    sortable: true,
    width: '120px',
    cell: (row) => row.trade[TRADE_COLUMN_IDS.SESSION] || '-',
  },
  {
    id: TRADE_COLUMN_IDS.R_MULTIPLE,
    header: 'R Multiple',
    sortable: true,
    width: '100px',
    align: 'right',
    cell: (row) => (
      <RMultipleCell rMultiple={row.trade[TRADE_COLUMN_IDS.R_MULTIPLE] || 0}>
        {row.trade[TRADE_COLUMN_IDS.R_MULTIPLE]
          ? `${row.trade[TRADE_COLUMN_IDS.R_MULTIPLE]?.toFixed(2)}R`
          : '-'}
      </RMultipleCell>
    ),
  },
  {
    id: TRADE_COLUMN_IDS.ACHIEVED_PL,
    header: 'P&L',
    sortable: true,
    width: '100px',
    align: 'right',
    cell: (row) => (
      <ProfitLossCell isProfit={(row.trade[TRADE_COLUMN_IDS.ACHIEVED_PL] || 0) > 0}>
        {formatCurrencyLocal(row.trade[TRADE_COLUMN_IDS.ACHIEVED_PL])}
      </ProfitLossCell>
    ),
  },
  {
    id: TRADE_COLUMN_IDS.PATTERN_QUALITY,
    header: 'Quality',
    sortable: true,
    width: '80px',
    align: 'center',
    cell: (row) => (
      <QualityRating rating={row.trade[TRADE_COLUMN_IDS.PATTERN_QUALITY] || 0}>
        {row.trade[TRADE_COLUMN_IDS.PATTERN_QUALITY]
          ? `${row.trade[TRADE_COLUMN_IDS.PATTERN_QUALITY]}/5`
          : '-'}
      </QualityRating>
    ),
  },
  {
    id: TRADE_COLUMN_IDS.WIN_LOSS,
    header: 'Result',
    sortable: true,
    width: '80px',
    align: 'center',
    cell: (row) => (
      <Badge
        variant={row.trade[TRADE_COLUMN_IDS.WIN_LOSS] === 'Win' ? 'success' : 'error'}
        size="small"
      >
        {row.trade[TRADE_COLUMN_IDS.WIN_LOSS] || '-'}
      </Badge>
    ),
  },
];
