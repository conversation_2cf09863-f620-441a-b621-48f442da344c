/**
 * Trade Component Types
 *
 * Shared types for trade-specific components
 */

export interface TradeMetric {
  label: string;
  value: string | number;
  positive?: boolean;
  negative?: boolean;
}

export interface TradeAnalysisData {
  title: string;
  content: React.ReactNode;
}

export interface SetupBuilderConfig {
  showPreview?: boolean;
  allowOptionalElements?: boolean;
  theme?: 'mercedes-green' | 'f1-official' | 'dark';
}
