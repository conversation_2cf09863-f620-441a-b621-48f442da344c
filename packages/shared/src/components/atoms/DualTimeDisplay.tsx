/**
 * Dual Time Display Component
 *
 * Displays time in both NY and local timezone with proper formatting.
 * Optimized for international traders working with NY sessions.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import {
  getCurrentDualTime,
  formatTimeForMobile,
  getTimeUntilNYTime,
  convertSessionToDualTime,
  type DualTimeDisplay as DualTimeData,
  type TimeInterval,
} from '../../utils/timeZoneUtils';

export interface DualTimeDisplayProps {
  /** Display mode */
  mode?: 'current' | 'static' | 'countdown' | 'session';
  /** Static NY time to display (for static mode) */
  nyTime?: string;
  /** Target NY time for countdown */
  targetNYTime?: string;
  /** Session start and end times (for session mode) */
  sessionStart?: string;
  sessionEnd?: string;
  /** Display format */
  format?: 'mobile' | 'desktop' | 'compact';
  /** Show live indicator */
  showLive?: boolean;
  /** Custom className */
  className?: string;
  /** Update interval in seconds (default: 1) */
  updateInterval?: number;
}

const TimeContainer = styled.div<{ format: string }>`
  display: flex;
  align-items: center;
  gap: ${({ format }) => (format === 'mobile' ? '4px' : '8px')};
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 600;
`;

const NYTime = styled.span`
  color: #3b82f6;
  font-size: inherit;
`;

const LocalTime = styled.span`
  color: #10b981;
  font-size: inherit;
`;

const Separator = styled.span`
  color: #6b7280;
  font-size: inherit;
`;

const Timezone = styled.span`
  color: #9ca3af;
  font-size: 0.85em;
  font-weight: 500;
`;

const LiveIndicator = styled.span`
  color: #ef4444;
  font-size: 0.75em;
  font-weight: bold;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;

const CountdownContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const CountdownValue = styled.span`
  color: #f59e0b;
  font-weight: bold;
`;

const CountdownLabel = styled.span`
  color: #9ca3af;
  font-size: 0.9em;
`;

/**
 * Current Time Display Component
 */
const CurrentTimeDisplay: React.FC<{
  format: string;
  showLive: boolean;
  updateInterval: number;
}> = ({ format, showLive, updateInterval }) => {
  const [dualTime, setDualTime] = useState<DualTimeData>(getCurrentDualTime());

  useEffect(() => {
    const timer = setInterval(() => {
      setDualTime(getCurrentDualTime());
    }, updateInterval * 1000);

    return () => clearInterval(timer);
  }, [updateInterval]);

  if (format === 'mobile') {
    return (
      <TimeContainer format={format}>
        <span>{formatTimeForMobile(dualTime)}</span>
        {showLive && <LiveIndicator>LIVE</LiveIndicator>}
      </TimeContainer>
    );
  }

  if (format === 'compact') {
    return (
      <TimeContainer format={format}>
        <NYTime>{dualTime.nyTime}</NYTime>
        <Separator>|</Separator>
        <LocalTime>{dualTime.localTime}</LocalTime>
        {showLive && <LiveIndicator>LIVE</LiveIndicator>}
      </TimeContainer>
    );
  }

  return (
    <TimeContainer format={format}>
      <NYTime>{dualTime.nyTime}</NYTime>
      <Timezone>{dualTime.nyTimezone}</Timezone>
      <Separator>|</Separator>
      <LocalTime>{dualTime.localTime}</LocalTime>
      <Timezone>{dualTime.localTimezone}</Timezone>
      {showLive && <LiveIndicator>LIVE</LiveIndicator>}
    </TimeContainer>
  );
};

/**
 * Static Time Display Component
 */
const StaticTimeDisplay: React.FC<{
  nyTime: string;
  format: string;
}> = ({ nyTime, format }) => {
  const dualTime = getCurrentDualTime();
  const sessionTime = convertSessionToDualTime(nyTime, nyTime);

  if (format === 'mobile') {
    return (
      <TimeContainer format={format}>
        <span>
          {sessionTime.localStart} 🇮🇪 | {nyTime} 🇺🇸
        </span>
      </TimeContainer>
    );
  }

  if (format === 'compact') {
    return (
      <TimeContainer format={format}>
        <NYTime>{nyTime}</NYTime>
        <Separator>|</Separator>
        <LocalTime>{sessionTime.localStart}</LocalTime>
      </TimeContainer>
    );
  }

  return (
    <TimeContainer format={format}>
      <NYTime>{nyTime}</NYTime>
      <Timezone>{dualTime.nyTimezone}</Timezone>
      <Separator>|</Separator>
      <LocalTime>{sessionTime.localStart}</LocalTime>
      <Timezone>{dualTime.localTimezone}</Timezone>
    </TimeContainer>
  );
};

/**
 * Countdown Display Component
 */
const CountdownDisplay: React.FC<{
  targetNYTime: string;
  format: string;
  updateInterval: number;
}> = ({ targetNYTime, format, updateInterval }) => {
  const [timeUntil, setTimeUntil] = useState<TimeInterval>(getTimeUntilNYTime(targetNYTime));

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeUntil(getTimeUntilNYTime(targetNYTime));
    }, updateInterval * 1000);

    return () => clearInterval(timer);
  }, [targetNYTime, updateInterval]);

  if (format === 'mobile') {
    return (
      <CountdownContainer>
        <CountdownValue>{timeUntil.formatted}</CountdownValue>
        <CountdownLabel>until {targetNYTime}</CountdownLabel>
      </CountdownContainer>
    );
  }

  return (
    <CountdownContainer>
      <CountdownLabel>Next in:</CountdownLabel>
      <CountdownValue>{timeUntil.formatted}</CountdownValue>
      <CountdownLabel>({targetNYTime} NY)</CountdownLabel>
    </CountdownContainer>
  );
};

/**
 * Session Time Display Component
 */
const SessionTimeDisplay: React.FC<{
  sessionStart: string;
  sessionEnd: string;
  format: string;
}> = ({ sessionStart, sessionEnd, format }) => {
  const sessionTime = convertSessionToDualTime(sessionStart, sessionEnd);

  if (format === 'mobile') {
    return (
      <TimeContainer format={format}>
        <span>{sessionTime.formatted}</span>
      </TimeContainer>
    );
  }

  if (format === 'compact') {
    return (
      <TimeContainer format={format}>
        <NYTime>
          {sessionStart}-{sessionEnd}
        </NYTime>
        <Separator>|</Separator>
        <LocalTime>
          {sessionTime.localStart}-{sessionTime.localEnd}
        </LocalTime>
      </TimeContainer>
    );
  }

  return (
    <TimeContainer format={format}>
      <div>
        <NYTime>
          {sessionStart}-{sessionEnd} NY
        </NYTime>
      </div>
      <Separator>|</Separator>
      <div>
        <LocalTime>
          {sessionTime.localStart}-{sessionTime.localEnd} Local
        </LocalTime>
      </div>
    </TimeContainer>
  );
};

/**
 * Main Dual Time Display Component
 */
export const DualTimeDisplay: React.FC<DualTimeDisplayProps> = (props) => {
  const {
    mode = 'current',
    nyTime,
    targetNYTime,
    sessionStart,
    sessionEnd,
    format = 'desktop',
    showLive = false,
    className,
    updateInterval = 1,
  } = props;
  const containerProps = {
    className,
    style: {
      fontSize: format === 'mobile' ? '14px' : format === 'compact' ? '13px' : '14px',
    },
  };

  switch (mode) {
    case 'static':
      if (!nyTime) {
        console.warn('DualTimeDisplay: nyTime is required for static mode');
        return null;
      }
      return (
        <div {...containerProps}>
          <StaticTimeDisplay nyTime={nyTime} format={format} />
        </div>
      );

    case 'countdown':
      if (!targetNYTime) {
        console.warn('DualTimeDisplay: targetNYTime is required for countdown mode');
        return null;
      }
      return (
        <div {...containerProps}>
          <CountdownDisplay
            targetNYTime={targetNYTime}
            format={format}
            updateInterval={updateInterval}
          />
        </div>
      );

    case 'session':
      if (!sessionStart || !sessionEnd) {
        console.warn('DualTimeDisplay: sessionStart and sessionEnd are required for session mode');
        return null;
      }
      return (
        <div {...containerProps}>
          <SessionTimeDisplay sessionStart={sessionStart} sessionEnd={sessionEnd} format={format} />
        </div>
      );

    case 'current':
    default:
      return (
        <div {...containerProps}>
          <CurrentTimeDisplay format={format} showLive={showLive} updateInterval={updateInterval} />
        </div>
      );
  }
};

export default DualTimeDisplay;
