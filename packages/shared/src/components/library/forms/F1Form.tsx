/**
 * F1Form Component
 *
 * Standardized F1-themed form component with built-in validation,
 * loading states, and consistent styling.
 *
 * PROVEN PATTERN: Extracted from QuickTradeForm and TradingPlan form patterns
 *
 * FEATURES:
 * - F1 racing theme consistency
 * - Built-in validation support
 * - Loading and error states
 * - Auto-save functionality
 * - Accessibility compliant
 */

import React, { ReactNode, FormEvent } from 'react';
import styled, { css } from 'styled-components';
import { spacing } from '../../../theme/tokens';

export interface F1FormProps {
  /** Form content */
  children: ReactNode;
  /** Form submission handler */
  onSubmit?: (e: FormEvent<HTMLFormElement>) => void | Promise<void>;
  /** Form title */
  title?: string;
  /** Form subtitle */
  subtitle?: string;
  /** Whether form is submitting */
  isSubmitting?: boolean;
  /** Form error message */
  error?: string | null;
  /** Success message */
  success?: string | null;
  /** Form variant */
  variant?: 'quick' | 'detailed' | 'modal' | 'inline';
  /** Whether to show F1 accent border */
  showAccent?: boolean;
  /** Custom className */
  className?: string;
  /** Whether form is disabled */
  disabled?: boolean;
  /** Auto-save functionality */
  autoSave?: boolean;
  /** Auto-save interval in ms */
  autoSaveInterval?: number;
}

const FormContainer = styled.form<{
  $variant: F1FormProps['variant'];
  $showAccent: boolean;
  $disabled: boolean;
}>`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  position: relative;

  /* Variant-specific styling */
  ${({ $variant }) => {
    switch ($variant) {
      case 'quick':
        return css`
          padding: ${spacing.lg};
          max-width: 600px;
        `;
      case 'detailed':
        return css`
          padding: ${spacing.xl};
          max-width: 800px;
        `;
      case 'modal':
        return css`
          padding: ${spacing.lg};
          max-width: 500px;
          margin: 0 auto;
        `;
      case 'inline':
        return css`
          padding: ${spacing.md};
          background: transparent;
          border: none;
        `;
      default:
        return css`
          padding: ${spacing.lg};
        `;
    }
  }}

  /* F1 Racing accent */
  ${({ $showAccent, theme }) =>
    $showAccent &&
    css`
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(
          90deg,
          ${theme.colors?.primary || '#dc2626'},
          ${theme.colors?.primaryDark || '#b91c1c'},
          ${theme.colors?.primary || '#dc2626'}
        );
        border-radius: ${theme.borderRadius?.lg || '8px'} ${theme.borderRadius?.lg || '8px'} 0 0;
      }
    `}

  /* Disabled state */
  ${({ $disabled }) =>
    $disabled &&
    css`
      opacity: 0.6;
      pointer-events: none;
    `}
`;

const FormHeader = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const FormTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;

const FormSubtitle = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  font-weight: 500;
`;

const FormContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const FormMessage = styled.div<{ $type: 'error' | 'success' | 'info' }>`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};

  ${({ $type }) => {
    switch ($type) {
      case 'error':
        return css`
          background: rgba(244, 67, 54, 0.1);
          border: 1px solid #f44336;
          color: #f44336;
        `;
      case 'success':
        return css`
          background: rgba(34, 197, 94, 0.1);
          border: 1px solid #22c55e;
          color: #22c55e;
        `;
      case 'info':
        return css`
          background: rgba(59, 130, 246, 0.1);
          border: 1px solid #3b82f6;
          color: #3b82f6;
        `;
    }
  }}
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  z-index: 10;
`;

const LoadingSpinner = styled.div`
  width: 32px;
  height: 32px;
  border: 3px solid #4b5563;
  border-top: 3px solid #dc2626;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

const AutoSaveIndicator = styled.div<{ $visible: boolean }>`
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  opacity: ${({ $visible }) => ($visible ? 1 : 0)};
  transition: opacity 0.3s ease;
`;

/**
 * F1Form Component
 *
 * Standardized form component that provides consistent styling,
 * validation support, and F1 racing theme.
 *
 * @example
 * ```typescript
 * // Quick trade form
 * <F1Form
 *   title="🏎️ Quick Trade Entry"
 *   variant="quick"
 *   showAccent={true}
 *   onSubmit={handleSubmit}
 *   isSubmitting={isLoading}
 *   error={error}
 *   autoSave={true}
 * >
 *   <TradeFormFields />
 * </F1Form>
 *
 * // Detailed form
 * <F1Form
 *   title="Trade Analysis"
 *   subtitle="Comprehensive trade details"
 *   variant="detailed"
 *   onSubmit={handleSubmit}
 * >
 *   <DetailedFormFields />
 * </F1Form>
 * ```
 */
export const F1Form: React.FC<F1FormProps> = (props) => {
  const {
    children,
    onSubmit,
    title,
    subtitle,
    isSubmitting = false,
    error = null,
    success = null,
    variant = 'quick',
    showAccent = true,
    className,
    disabled = false,
    autoSave = false,
  } = props;
  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (onSubmit && !isSubmitting && !disabled) {
      await onSubmit(e);
    }
  };

  return (
    <FormContainer
      $variant={variant as NonNullable<F1FormProps['variant']>}
      $showAccent={showAccent}
      $disabled={disabled}
      className={className}
      onSubmit={handleSubmit}
      noValidate
    >
      {/* Loading overlay */}
      {isSubmitting && (
        <LoadingOverlay>
          <LoadingSpinner />
        </LoadingOverlay>
      )}

      {/* Auto-save indicator */}
      {autoSave && (
        <AutoSaveIndicator $visible={!isSubmitting}>Auto-save enabled</AutoSaveIndicator>
      )}

      {/* Form header */}
      {(title || subtitle) && (
        <FormHeader>
          {title && <FormTitle>{title}</FormTitle>}
          {subtitle && <FormSubtitle>{subtitle}</FormSubtitle>}
        </FormHeader>
      )}

      {/* Error message */}
      {error && <FormMessage $type="error">⚠️ {error}</FormMessage>}

      {/* Success message */}
      {success && <FormMessage $type="success">✅ {success}</FormMessage>}

      {/* Form content */}
      <FormContent>{children}</FormContent>
    </FormContainer>
  );
};

export default F1Form;
