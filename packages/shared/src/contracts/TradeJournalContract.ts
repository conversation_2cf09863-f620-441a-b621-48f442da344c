/**
 * Trade Journal Feature Contract
 *
 * Defines the interface for the Trade Journal feature that other features
 * can use without directly importing Trade Journal components.
 */

import { Trade, SetupComponents } from '../types';

export interface TradeJournalApi {
  /**
   * Get trade data by ID
   */
  getTradeData: (id: number) => Promise<Trade | null>;

  /**
   * Validate setup components
   */
  validateSetup: (setup: SetupComponents) => {
    isValid: boolean;
    errors: string[];
  };

  /**
   * Get recent trades
   */
  getRecentTrades: (limit?: number) => Promise<Trade[]>;

  /**
   * Calculate trade metrics
   */
  calculateMetrics: (trades: Trade[]) => {
    totalTrades: number;
    winRate: number;
    avgPnL: number;
    totalPnL: number;
  };

  /**
   * Export trades data
   */
  exportTrades: (trades: Trade[], format: 'csv' | 'json') => Promise<string>;
}

export interface TradeJournalEvents {
  /**
   * Emitted when a new trade is created
   */
  onTradeCreated: (trade: Trade) => void;

  /**
   * Emitted when a trade is updated
   */
  onTradeUpdated: (trade: Trade) => void;

  /**
   * Emitted when a trade is deleted
   */
  onTradeDeleted: (tradeId: number) => void;

  /**
   * Emitted when trades are filtered
   */
  onTradesFiltered: (trades: Trade[]) => void;
}

export interface TradeJournalState {
  trades: Trade[];
  isLoading: boolean;
  error: string | null;
  selectedTrade: Trade | null;
  filters: {
    dateRange?: [Date, Date];
    direction?: 'Long' | 'Short';
    market?: string;
    setup?: string;
  };
}
