/**
 * useSessionSelection Hook
 *
 * React hook for managing hierarchical session selection with intelligent
 * validation and backward compatibility.
 */

import { useState, useCallback, useMemo, useEffect } from 'react';
import {
  SessionSelection,
  SessionType,
  MacroPeriodType,
  TimeRange,
  SessionFilterOptions,
  TimeValidationResult,
} from '../types/tradingSessions';
import { SessionUtils } from '../utils/sessionUtils';

export interface UseSessionSelectionOptions {
  /** Initial session selection */
  initialSelection?: SessionSelection;
  /** Whether to auto-detect current session */
  autoDetectCurrent?: boolean;
  /** Filter options for available sessions/macros */
  filterOptions?: SessionFilterOptions;
  /** Callback when selection changes */
  onSelectionChange?: (selection: SessionSelection) => void;
  /** Whether to validate time inputs */
  validateTimes?: boolean;
}

export interface UseSessionSelectionReturn {
  // Current selection state
  selection: SessionSelection;

  // Selection methods
  selectSession: (sessionType: SessionType) => void;
  selectMacro: (macroType: MacroPeriodType) => void;
  selectCustomRange: (timeRange: TimeRange) => void;
  clearSelection: () => void;

  // Validation
  validateTime: (time: string) => TimeValidationResult;
  isValidSelection: boolean;

  // Available options (filtered)
  availableSessions: Array<{ value: SessionType; label: string; group: string }>;
  availableMacros: Array<{
    value: MacroPeriodType;
    label: string;
    group: string;
    parentSession: SessionType;
  }>;

  // Hierarchical options (grouped by parent session)
  hierarchicalOptions: Array<{
    session: SessionType;
    sessionLabel: string;
    macros: Array<{ value: MacroPeriodType; label: string }>;
  }>;

  // Current session detection
  currentSession: SessionSelection | null;
  isCurrentSessionActive: boolean;

  // Utility methods
  getSessionDetails: (sessionType: SessionType) => any;
  getMacroDetails: (macroType: MacroPeriodType) => any;
  convertLegacySession: (legacySession: string) => SessionSelection | null;
}

/**
 * useSessionSelection Hook
 */
export const useSessionSelection = (
  options: UseSessionSelectionOptions = {}
): UseSessionSelectionReturn => {
  const {
    initialSelection,
    autoDetectCurrent = false,
    filterOptions = {},
    onSelectionChange,
    validateTimes = true,
  } = options;

  // State
  const [selection, setSelection] = useState<SessionSelection>(
    initialSelection || {
      displayLabel: 'No Selection',
      selectionType: 'custom',
    }
  );

  // Get current session
  const currentSession = useMemo(() => {
    return SessionUtils.getCurrentSession();
  }, []);

  // Check if current session is active
  const isCurrentSessionActive = useMemo(() => {
    return currentSession !== null;
  }, [currentSession]);

  // Get filtered options
  const { availableSessions, availableMacros } = useMemo(() => {
    const { sessions, macros } = SessionUtils.filterSessions(filterOptions);
    const { sessionOptions, macroOptions } = SessionUtils.getDisplayOptions();

    const filteredSessionOptions = sessionOptions.filter((opt) =>
      sessions.some((s) => s.type === opt.value)
    );

    const filteredMacroOptions = macroOptions.filter((opt) =>
      macros.some((m) => m.type === opt.value)
    );

    return {
      availableSessions: filteredSessionOptions,
      availableMacros: filteredMacroOptions,
    };
  }, [filterOptions]);

  // Create hierarchical options
  const hierarchicalOptions = useMemo(() => {
    // const hierarchy = SessionUtils.getSessionHierarchy(); // Available for future session-specific logic

    return availableSessions.map((sessionOpt) => {
      // const session = hierarchy.sessionsByType[sessionOpt.value]; // Available if needed for future enhancements
      const sessionMacros = availableMacros
        .filter((macro) => macro.parentSession === sessionOpt.value)
        .map((macro) => ({
          value: macro.value,
          label: macro.label,
        }));

      return {
        session: sessionOpt.value,
        sessionLabel: sessionOpt.label,
        macros: sessionMacros,
      };
    });
  }, [availableSessions, availableMacros]);

  // Auto-detect current session on mount
  useEffect(() => {
    if (autoDetectCurrent && currentSession && !initialSelection) {
      setSelection(currentSession);
    }
  }, [autoDetectCurrent, currentSession, initialSelection]);

  // Notify on selection change
  useEffect(() => {
    onSelectionChange?.(selection);
  }, [selection, onSelectionChange]);

  // Selection methods
  const selectSession = useCallback((sessionType: SessionType) => {
    const newSelection = SessionUtils.createSessionSelection(sessionType);
    setSelection(newSelection);
  }, []);

  const selectMacro = useCallback((macroType: MacroPeriodType) => {
    const newSelection = SessionUtils.createSessionSelection(undefined, macroType);
    setSelection(newSelection);
  }, []);

  const selectCustomRange = useCallback((timeRange: TimeRange) => {
    const newSelection = SessionUtils.createSessionSelection(undefined, undefined, timeRange);
    setSelection(newSelection);
  }, []);

  const clearSelection = useCallback(() => {
    setSelection({
      displayLabel: 'No Selection',
      selectionType: 'custom',
    });
  }, []);

  // Validation
  const validateTime = useCallback(
    (time: string): TimeValidationResult => {
      if (!validateTimes) {
        return { isValid: true };
      }
      return SessionUtils.validateTime(time);
    },
    [validateTimes]
  );

  const isValidSelection = useMemo(() => {
    if (selection.selectionType === 'session' && selection.session) {
      return SessionUtils.getSession(selection.session) !== null;
    }
    if (selection.selectionType === 'macro' && selection.macroPeriod) {
      return SessionUtils.getMacroPeriod(selection.macroPeriod) !== null;
    }
    if (selection.selectionType === 'custom' && selection.customTimeRange) {
      const startValid = validateTime(selection.customTimeRange.start);
      const endValid = validateTime(selection.customTimeRange.end);
      return startValid.isValid && endValid.isValid;
    }
    return selection.selectionType === 'custom' && !selection.customTimeRange;
  }, [selection, validateTime]);

  // Utility methods
  const getSessionDetails = useCallback((sessionType: SessionType) => {
    return SessionUtils.getSession(sessionType);
  }, []);

  const getMacroDetails = useCallback((macroType: MacroPeriodType) => {
    return SessionUtils.getMacroPeriod(macroType);
  }, []);

  const convertLegacySession = useCallback((legacySession: string) => {
    return SessionUtils.convertLegacySession(legacySession);
  }, []);

  return {
    // State
    selection,

    // Selection methods
    selectSession,
    selectMacro,
    selectCustomRange,
    clearSelection,

    // Validation
    validateTime,
    isValidSelection,

    // Options
    availableSessions,
    availableMacros,
    hierarchicalOptions,

    // Current session
    currentSession,
    isCurrentSessionActive,

    // Utilities
    getSessionDetails,
    getMacroDetails,
    convertLegacySession,
  };
};
