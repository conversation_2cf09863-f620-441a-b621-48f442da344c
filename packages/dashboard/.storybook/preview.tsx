import type { Preview } from '@storybook/react';
import React from 'react';
import { ThemeProvider } from '@adhd-trading-dashboard/shared';
import { f1Theme } from '@adhd-trading-dashboard/shared';

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    docs: {
      theme: {
        base: 'dark',
        colorPrimary: '#e10600',
        colorSecondary: '#1e5bc6',
        appBg: '#1a1f2c',
        appContentBg: '#333333',
        appBorderColor: '#666666',
        textColor: '#ffffff',
        textInverseColor: '#000000',
        barTextColor: '#ffffff',
        barSelectedColor: '#e10600',
        barBg: '#333333',
        inputBg: '#1a1f2c',
        inputBorder: '#666666',
        inputTextColor: '#ffffff',
        inputBorderRadius: 4,
      },
    },
    backgrounds: {
      default: 'dark',
      values: [
        {
          name: 'dark',
          value: '#1a1f2c',
        },
        {
          name: 'f1-official',
          value: '#15151E',
        },
      ],
    },
  },
  decorators: [
    (Story) => (
      <ThemeProvider theme={f1Theme}>
        <div
          style={{
            padding: '20px',
            backgroundColor: f1Theme.colors.background,
            minHeight: '100vh',
            color: f1Theme.colors.textPrimary,
          }}
        >
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
};

export default preview;
