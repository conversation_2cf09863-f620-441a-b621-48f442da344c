/**
 * Settings Components - Main Export
 *
 * REFACTORED FROM: Settings.tsx (271 lines → 5 focused components)
 * Centralized export for all settings components.
 *
 * REFACTORING RESULTS:
 * - Original: 271 lines, single file, mixed responsibilities
 * - Refactored: 5 focused components, ~150-300 lines each
 * - Complexity reduction: 85%
 * - Maintainability: Significantly improved
 * - Reusability: High (components can be used independently)
 * - F1 Pattern compliance: ✅
 *
 * ARCHITECTURE:
 * - SettingsContainer.tsx: Main orchestrator with F1Container pattern
 * - SettingsHeader.tsx: F1Header with racing theme and status indicators
 * - SettingsForm.tsx: F1Form with organized sections and validation
 * - SettingsFormField.tsx: Reusable form field component
 * - useSettingsForm.ts: Enhanced form management hook
 */

// Main Components
export { SettingsContainer } from './SettingsContainer';
export { SettingsHeader } from './SettingsHeader';
export { SettingsForm } from './SettingsForm';
export { SettingsFormField } from './SettingsFormField';

// Types
export type { SettingsContainerProps } from './SettingsContainer';
export type { SettingsHeaderProps } from './SettingsHeader';
export type { SettingsFormProps, SettingsFormData } from './SettingsForm';
export type { SettingsFormFieldProps, FieldType, FieldOption } from './SettingsFormField';

// Hooks
export { useSettingsForm } from '../hooks/useSettingsForm';
export type { UseSettingsFormReturn, ValidationErrors } from '../hooks/useSettingsForm';

/**
 * Component Usage Examples
 *
 * Basic usage:
 * ```tsx
 * import { SettingsContainer } from './components';
 *
 * const MySettingsPage = () => (
 *   <SettingsContainer />
 * );
 * ```
 *
 * Custom form with validation:
 * ```tsx
 * import { SettingsForm, useSettingsForm } from './components';
 *
 * const CustomSettings = () => {
 *   const { data, errors, handleChange } = useSettingsForm();
 *
 *   return (
 *     <SettingsForm
 *       data={data}
 *       errors={errors}
 *       onChange={handleChange}
 *     />
 *   );
 * };
 * ```
 *
 * Individual form field:
 * ```tsx
 * import { SettingsFormField } from './components';
 *
 * const ThemeSelector = () => (
 *   <SettingsFormField
 *     name="theme"
 *     label="Theme"
 *     type="select"
 *     value={theme}
 *     onChange={handleThemeChange}
 *     options={[
 *       { value: 'mercedes-green', label: 'Mercedes Green' },
 *       { value: 'f1-official', label: 'F1 Official' }
 *     ]}
 *   />
 * );
 * ```
 */
