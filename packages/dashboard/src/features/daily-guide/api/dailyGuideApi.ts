/**
 * Daily Guide API
 *
 * API functions for the daily guide feature
 */

import { DailyGuideData, MarketSentiment, TradingPlanPriority } from '../types';

/**
 * Fetch daily guide data from the API
 *
 * In a real application, this would make an actual API call.
 * For now, we're generating mock data.
 */
export const fetchDailyGuideData = async (): Promise<DailyGuideData> => {
  // Simulate API call delay
  await new Promise((resolve) => setTimeout(resolve, 800));

  // Randomly decide if we should throw an error (for testing error handling)
  const shouldError = Math.random() < 0.05; // 5% chance of error
  if (shouldError) {
    throw new Error('Failed to fetch daily guide data');
  }

  return generateMockData();
};

/**
 * Generate mock data for development and testing
 */
const generateMockData = (): DailyGuideData => {
  // Market data
  const sentiments: MarketSentiment[] = ['bullish', 'bearish', 'neutral'];
  const randomSentiment = sentiments[Math.floor(Math.random() * sentiments.length)];

  const marketData = {
    sentiment: randomSentiment,
    summary: getSummaryForSentiment(randomSentiment),
    indices: [
      {
        name: 'S&P 500',
        value: 4500 + Math.random() * 100,
        change:
          (Math.random() * 2 - 1) *
          (randomSentiment === 'bullish' ? 1 : randomSentiment === 'bearish' ? -1 : 0.5),
        previousClose: 4500 + Math.random() * 50,
      },
      {
        name: 'Nasdaq',
        value: 14000 + Math.random() * 500,
        change:
          (Math.random() * 2 - 1) *
          (randomSentiment === 'bullish' ? 1 : randomSentiment === 'bearish' ? -1 : 0.5),
        previousClose: 14000 + Math.random() * 250,
      },
      {
        name: 'Dow Jones',
        value: 35000 + Math.random() * 500,
        change:
          (Math.random() * 2 - 1) *
          (randomSentiment === 'bullish' ? 1 : randomSentiment === 'bearish' ? -1 : 0.5),
        previousClose: 35000 + Math.random() * 250,
      },
      {
        name: 'VIX',
        value: 15 + Math.random() * 10,
        change:
          (Math.random() * 2 - 1) *
          (randomSentiment === 'bullish' ? -1 : randomSentiment === 'bearish' ? 1 : 0.5),
        previousClose: 15 + Math.random() * 5,
      },
    ],
    lastUpdated: new Date().toISOString(),
  };

  // Trading plan
  // Using different priorities for each item
  // const priorities: TradingPlanPriority[] = ['high', 'medium', 'low'];
  const tradingPlan = [
    {
      id: '1',
      description: 'Wait for market open before placing any trades',
      priority: 'high' as TradingPlanPriority,
      completed: false,
    },
    {
      id: '2',
      description: 'Focus on tech sector for long opportunities',
      priority: 'medium' as TradingPlanPriority,
      completed: false,
    },
    {
      id: '3',
      description: 'Use tight stop losses due to expected volatility',
      priority: 'high' as TradingPlanPriority,
      completed: false,
    },
    {
      id: '4',
      description: 'Review earnings reports for potential opportunities',
      priority: 'medium' as TradingPlanPriority,
      completed: false,
    },
    {
      id: '5',
      description: 'Avoid over-trading in the first hour',
      priority: 'low' as TradingPlanPriority,
      completed: false,
    },
  ];

  // Key levels
  const keyLevels = [
    {
      symbol: 'SPY',
      support: ['450.00', '445.75', '442.30'],
      resistance: ['455.50', '460.00', '462.75'],
      pivotPoint: '452.25',
    },
    {
      symbol: 'QQQ',
      support: ['365.20', '360.00', '355.50'],
      resistance: ['370.00', '375.35', '380.00'],
      pivotPoint: '367.75',
    },
    {
      symbol: 'AAPL',
      support: ['175.00', '170.50', '165.75'],
      resistance: ['180.00', '185.50', '190.25'],
      pivotPoint: '177.25',
    },
  ];

  // Market news
  const marketNews = [
    {
      id: '1',
      title: 'Fed signals potential rate hike in upcoming meeting',
      source: 'Financial Times',
      url: 'https://example.com/news/1',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      impact: 'high' as const,
    },
    {
      id: '2',
      title: 'Tech stocks rally on strong earnings reports',
      source: 'Wall Street Journal',
      url: 'https://example.com/news/2',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
      impact: 'medium' as const,
    },
    {
      id: '3',
      title: 'Oil prices stabilize after recent volatility',
      source: 'Bloomberg',
      url: 'https://example.com/news/3',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
      impact: 'low' as const,
    },
  ];

  return {
    // marketData, // Removed - not part of DailyGuideData interface
    tradingPlan: {
      items: tradingPlan,
      strategy: 'Focus on FVG and RD setups during high-volume sessions',
      riskManagement: {
        maxRiskPerTrade: 1.5,
        maxDailyLoss: 3.0,
        maxTrades: 5,
        positionSizing: '1% of account per trade',
      },
      notes: 'Market showing signs of consolidation. Be patient and wait for clear setups.',
    },
    keyLevels,
    marketNews,
  };
};

/**
 * Get a summary based on market sentiment
 */
const getSummaryForSentiment = (sentiment: MarketSentiment): string => {
  switch (sentiment) {
    case 'bullish':
      return 'Markets are showing strong bullish momentum with tech stocks leading the rally. Watch for potential breakouts above key resistance levels.';
    case 'bearish':
      return 'Markets are under pressure with broad-based selling. Focus on defensive sectors and watch key support levels for potential bounces.';
    case 'neutral':
      return 'Markets are showing mixed signals with sector rotation evident. Range-bound trading is likely until a clear catalyst emerges.';
    default:
      return 'Markets are showing mixed signals. Monitor key levels for potential trading opportunities.';
  }
};
