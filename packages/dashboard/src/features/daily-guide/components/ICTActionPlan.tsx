/**
 * ICT Action Plan Component
 *
 * Replaces generic Trading Plan with sophisticated ICT-specific action items
 * based on real trading performance data and model analysis.
 */

import React from 'react';
import styled from 'styled-components';
import { Card } from '@adhd-trading-dashboard/shared';
import { useICTActionPlan } from '../hooks/useICTActionPlan';

export interface ICTActionPlanProps {
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when the refresh button is clicked */
  onRefresh?: () => void;
  /** Additional class name */
  className?: string;
}

// Styled components with F1 theme
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const SectionTitle = styled.h3`
  color: #ffffff;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PerformanceCard = styled.div`
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05));
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
`;

const PerformanceGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 16px;
`;

const PerformanceMetric = styled.div`
  text-align: center;
`;

const MetricValue = styled.div`
  font-size: 24px;
  font-weight: 800;
  color: #ffffff;
`;

const MetricLabel = styled.div`
  font-size: 12px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 4px;
`;

const ModelComparisonCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
`;

const ModelGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
`;

const ModelCard = styled.div<{ isRecommended: boolean }>`
  background: ${({ isRecommended }) =>
    isRecommended ? 'var(--model-card-bg)' : 'rgba(255, 255, 255, 0.03)'};
  border: 1px solid
    ${({ isRecommended }) =>
      isRecommended ? 'var(--model-card-border)' : 'rgba(255, 255, 255, 0.1)'};
  border-radius: 8px;
  padding: 16px;
  text-align: center;
`;

const ModelName = styled.div<{ isRecommended: boolean }>`
  font-size: 16px;
  font-weight: 700;
  color: ${({ isRecommended }) => (isRecommended ? 'var(--model-name-color)' : '#ffffff')};
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ModelStats = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 8px;
`;

const ModelStat = styled.div`
  font-size: 12px;
  color: #d1d5db;
`;

const RecommendationBadge = styled.div<{ isRecommended: boolean }>`
  background: ${({ isRecommended }) => (isRecommended ? '#dc2626' : '#6b7280')};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin-top: 8px;
`;

const ActionItemsCard = styled.div`
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
`;

const ActionItemsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const ActionItem = styled.div<{ priority: 'HIGH' | 'MEDIUM' | 'LOW' }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px 16px;
  border-left: 4px solid
    ${({ priority }) => {
      switch (priority) {
        case 'HIGH':
          return '#dc2626';
        case 'MEDIUM':
          return '#f59e0b';
        default:
          return '#6b7280';
      }
    }};
`;

const ActionText = styled.div`
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
`;

const PriorityBadge = styled.div<{ priority: 'HIGH' | 'MEDIUM' | 'LOW' }>`
  background: ${({ priority }) => {
    switch (priority) {
      case 'HIGH':
        return '#dc2626';
      case 'MEDIUM':
        return '#f59e0b';
      default:
        return '#6b7280';
    }
  }};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
`;

const RiskManagementCard = styled.div`
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(147, 51, 234, 0.05));
  border: 1px solid rgba(168, 85, 247, 0.3);
  border-radius: 12px;
  padding: 20px;
`;

const RiskGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
`;

const RiskItem = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
`;

const RiskValue = styled.div`
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
`;

const RiskLabel = styled.div`
  font-size: 11px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 4px;
`;

const QualityChecklistCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
`;

const ChecklistGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
`;

const ChecklistItem = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #d1d5db;
`;

const CheckIcon = styled.div`
  color: #10b981;
  font-weight: bold;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const EmptyTitle = styled.h3`
  color: #ffffff;
  font-size: 18px;
  margin-bottom: 8px;
`;

const EmptyMessage = styled.p`
  color: #9ca3af;
  font-size: 14px;
  line-height: 1.5;
`;

/**
 * ICT Action Plan Component
 */
export const ICTActionPlan: React.FC<ICTActionPlanProps> = ({
  isLoading = false,
  error = null,
  onRefresh,
  className,
}) => {
  const { actionPlan, isLoading: planLoading, error: planError } = useICTActionPlan();

  const loading = isLoading || planLoading;
  const displayError = error || planError;

  // Loading state
  if (loading) {
    return (
      <Card title="📋 ICT Action Plan">
        <div style={{ padding: '24px', textAlign: 'center' }}>
          Generating data-driven ICT action plan...
        </div>
      </Card>
    );
  }

  // Error state
  if (displayError) {
    return (
      <Card title="📋 ICT Action Plan">
        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>
          Error: {displayError}
          {onRefresh && (
            <button
              onClick={onRefresh}
              style={{
                marginLeft: '16px',
                padding: '8px 16px',
                background: '#f0f0f0',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Retry
            </button>
          )}
        </div>
      </Card>
    );
  }

  // Empty state
  if (!actionPlan) {
    return (
      <Card title="📋 ICT Action Plan">
        <EmptyState>
          <EmptyIcon>📋</EmptyIcon>
          <EmptyTitle>No Trading Data Available</EmptyTitle>
          <EmptyMessage>
            Import your trading data to generate a personalized ICT action plan based on your actual
            performance patterns.
          </EmptyMessage>
        </EmptyState>
      </Card>
    );
  }

  return (
    <Card
      title="📋 ICT Action Plan"
      actions={
        onRefresh ? (
          <button
            onClick={onRefresh}
            style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }}
          >
            🔄 Refresh
          </button>
        ) : undefined
      }
    >
      <Container className={className}>
        {/* Performance Analysis */}
        <div>
          <SectionTitle>📊 This Week's Performance Analysis</SectionTitle>
          <PerformanceCard>
            <PerformanceGrid>
              <PerformanceMetric>
                <MetricValue>{actionPlan.weeklyPerformance.totalTrades}</MetricValue>
                <MetricLabel>Total Trades</MetricLabel>
              </PerformanceMetric>
              <PerformanceMetric>
                <MetricValue>{actionPlan.weeklyPerformance.winRate.toFixed(0)}%</MetricValue>
                <MetricLabel>Win Rate</MetricLabel>
              </PerformanceMetric>
              <PerformanceMetric>
                <MetricValue>{actionPlan.weeklyPerformance.avgRMultiple.toFixed(1)}R</MetricValue>
                <MetricLabel>Avg R-Multiple</MetricLabel>
              </PerformanceMetric>
              <PerformanceMetric>
                <MetricValue>{actionPlan.weeklyPerformance.avgQuality.toFixed(1)}</MetricValue>
                <MetricLabel>Avg Quality</MetricLabel>
              </PerformanceMetric>
            </PerformanceGrid>
          </PerformanceCard>
        </div>

        {/* Model Selection Strategy */}
        <div>
          <SectionTitle>🎯 Model Selection Strategy</SectionTitle>
          <ModelComparisonCard>
            <ModelGrid>
              <ModelCard isRecommended={actionPlan.modelStrategy.recommendation === 'RD-Cont'}>
                <ModelName isRecommended={actionPlan.modelStrategy.recommendation === 'RD-Cont'}>
                  RD-Cont
                </ModelName>
                <ModelStats>
                  <ModelStat>{actionPlan.modelStrategy.rdCont.trades} trades</ModelStat>
                  <ModelStat>{actionPlan.modelStrategy.rdCont.winRate.toFixed(0)}% win</ModelStat>
                </ModelStats>
                <RecommendationBadge
                  isRecommended={actionPlan.modelStrategy.recommendation === 'RD-Cont'}
                >
                  {actionPlan.modelStrategy.recommendation === 'RD-Cont'
                    ? 'PRIORITIZE'
                    : 'SECONDARY'}
                </RecommendationBadge>
              </ModelCard>

              <ModelCard isRecommended={actionPlan.modelStrategy.recommendation === 'FVG-RD'}>
                <ModelName isRecommended={actionPlan.modelStrategy.recommendation === 'FVG-RD'}>
                  FVG-RD
                </ModelName>
                <ModelStats>
                  <ModelStat>{actionPlan.modelStrategy.fvgRd.trades} trades</ModelStat>
                  <ModelStat>{actionPlan.modelStrategy.fvgRd.winRate.toFixed(0)}% win</ModelStat>
                </ModelStats>
                <RecommendationBadge
                  isRecommended={actionPlan.modelStrategy.recommendation === 'FVG-RD'}
                >
                  {actionPlan.modelStrategy.recommendation === 'FVG-RD'
                    ? 'PRIORITIZE'
                    : 'SECONDARY'}
                </RecommendationBadge>
              </ModelCard>
            </ModelGrid>

            <div
              style={{ textAlign: 'center', marginTop: '16px', color: '#d1d5db', fontSize: '14px' }}
            >
              <strong>Current Recommendation:</strong> {actionPlan.modelStrategy.reasoning}
            </div>
          </ModelComparisonCard>
        </div>

        {/* Priority Action Items */}
        <div>
          <SectionTitle>🔥 Priority Action Items</SectionTitle>
          <ActionItemsCard>
            <ActionItemsList>
              {actionPlan.actionItems.map((item, index) => (
                <ActionItem key={index} priority={item.priority}>
                  <ActionText>{item.description}</ActionText>
                  <PriorityBadge priority={item.priority}>{item.priority}</PriorityBadge>
                </ActionItem>
              ))}
            </ActionItemsList>
          </ActionItemsCard>
        </div>

        {/* Risk Management */}
        <div>
          <SectionTitle>🛡️ Risk Management (Your Data)</SectionTitle>
          <RiskManagementCard>
            <RiskGrid>
              <RiskItem>
                <RiskValue>{actionPlan.riskManagement.avgRisk.toFixed(0)}</RiskValue>
                <RiskLabel>Avg Risk (Points)</RiskLabel>
              </RiskItem>
              <RiskItem>
                <RiskValue>{actionPlan.riskManagement.targetRMultiple.toFixed(1)}R</RiskValue>
                <RiskLabel>Target R-Multiple</RiskLabel>
              </RiskItem>
              <RiskItem>
                <RiskValue>{actionPlan.riskManagement.positionSizing}</RiskValue>
                <RiskLabel>Position Sizing</RiskLabel>
              </RiskItem>
              <RiskItem>
                <RiskValue>{actionPlan.riskManagement.stopLossStrategy}</RiskValue>
                <RiskLabel>Stop Loss</RiskLabel>
              </RiskItem>
            </RiskGrid>
          </RiskManagementCard>
        </div>

        {/* Quality Control Checklist */}
        <div>
          <SectionTitle>📝 Quality Control Checklist</SectionTitle>
          <QualityChecklistCard>
            <ChecklistGrid>
              {actionPlan.qualityChecklist.map((item, index) => (
                <ChecklistItem key={index}>
                  <CheckIcon>✅</CheckIcon>
                  {item}
                </ChecklistItem>
              ))}
            </ChecklistGrid>
          </QualityChecklistCard>
        </div>
      </Container>
    </Card>
  );
};
