/**
 * Elite ICT Intelligence Component
 *
 * Comprehensive trading intelligence system integrating:
 * - Intelligent Model Selection Engine
 * - Advanced Pattern Quality Scoring
 * - Granular Session Intelligence
 * - Predictive Intelligence Layer
 */

import React from 'react';
import styled from 'styled-components';
import { Card } from '@adhd-trading-dashboard/shared';
import { useModelSelectionEngine } from '../hooks/useModelSelectionEngine';
import { usePatternQualityScoring } from '../hooks/usePatternQualityScoring';
import { useGranularSessionIntelligence } from '../hooks/useGranularSessionIntelligence';
import { useSuccessProbabilityCalculator } from '../hooks/useSuccessProbabilityCalculator';
import { useEnhancedSetupIntelligence } from '../hooks/useEnhancedSetupIntelligence';

export interface EliteICTIntelligenceProps {
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when the refresh button is clicked */
  onRefresh?: () => void;
  /** Additional class name */
  className?: string;
}

// Styled components with F1 theme
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const IntelligenceGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const Section = styled.div`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  color: #ffffff;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ModelRecommendationCard = styled.div`
  background: var(--model-card-bg);
  border: 1px solid var(--model-card-border);
  border-radius: 12px;
  padding: 20px;
  position: relative;
  overflow: hidden;
`;

const ModelHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 16px;
`;

const RecommendedModel = styled.div`
  font-size: 24px;
  font-weight: 800;
  color: var(--model-name-color);
  text-transform: uppercase;
  letter-spacing: 1px;
`;

const ProbabilityBadge = styled.div<{ confidence: string }>`
  background: ${({ confidence }) => {
    switch (confidence) {
      case 'HIGH':
        return 'linear-gradient(90deg, #10b981, #059669)';
      case 'MEDIUM':
        return 'linear-gradient(90deg, #f59e0b, #d97706)';
      default:
        return 'linear-gradient(90deg, #6b7280, #4b5563)';
    }
  }};
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
`;

const ConditionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin: 16px 0;
`;

const ConditionItem = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
`;

const ConditionLabel = styled.div`
  color: #9ca3af;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
`;

const ConditionValue = styled.div`
  color: #ffffff;
  font-weight: 600;
`;

const ReasoningText = styled.div`
  color: #d1d5db;
  font-size: 14px;
  line-height: 1.5;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
`;

const PatternQualityCard = styled.div<{ rating: string }>`
  background: ${({ rating }) => {
    switch (rating) {
      case 'EXCEPTIONAL':
        return 'var(--pattern-excellent-bg)';
      case 'EXCELLENT':
        return 'var(--pattern-excellent-bg)';
      case 'GOOD':
        return 'var(--pattern-good-bg)';
      case 'FAIR':
        return 'var(--pattern-average-bg)';
      default:
        return 'var(--pattern-poor-bg)';
    }
  }};
  border: 1px solid
    ${({ rating }) => {
      switch (rating) {
        case 'EXCEPTIONAL':
          return 'var(--pattern-excellent-border)';
        case 'EXCELLENT':
          return 'var(--pattern-excellent-border)';
        case 'GOOD':
          return 'var(--pattern-good-border)';
        case 'FAIR':
          return 'var(--pattern-average-border)';
        default:
          return 'var(--pattern-poor-border)';
      }
    }};
  border-radius: 12px;
  padding: 20px;
`;

const QualityScore = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
`;

const ScoreValue = styled.div`
  font-size: 32px;
  font-weight: 800;
  color: #ffffff;
`;

const ScoreMax = styled.div`
  font-size: 16px;
  color: #9ca3af;
`;

const QualityRating = styled.div<{ rating: string }>`
  background: ${({ rating }) => {
    switch (rating) {
      case 'EXCEPTIONAL':
        return 'var(--pattern-excellent-text)';
      case 'EXCELLENT':
        return 'var(--pattern-excellent-text)';
      case 'GOOD':
        return 'var(--pattern-good-text)';
      case 'FAIR':
        return 'var(--pattern-average-text)';
      default:
        return 'var(--pattern-poor-text)';
    }
  }};
  color: ${({ rating }) => {
    switch (rating) {
      case 'EXCEPTIONAL':
        return 'var(--bg-primary)';
      case 'EXCELLENT':
        return 'var(--bg-primary)';
      case 'GOOD':
        return 'var(--text-primary)';
      case 'FAIR':
        return 'var(--bg-primary)';
      default:
        return 'var(--text-primary)';
    }
  }};
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
`;

const BreakdownGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin: 16px 0;
`;

const BreakdownItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  font-size: 12px;
`;

const BreakdownLabel = styled.div`
  color: #9ca3af;
`;

const BreakdownScore = styled.div`
  color: #ffffff;
  font-weight: 600;
`;

const SessionWindowCard = styled.div<{ isActive: boolean; isOptimal: boolean }>`
  background: ${({ isActive, isOptimal }) => {
    if (isActive && isOptimal)
      return 'linear-gradient(135deg, rgba(220, 38, 38, 0.2), rgba(239, 68, 68, 0.1))';
    if (isActive) return 'linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1))';
    if (isOptimal)
      return 'linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05))';
    return 'rgba(255, 255, 255, 0.05)';
  }};
  border: 1px solid
    ${({ isActive, isOptimal, theme }) => {
      if (isActive && isOptimal) return `${theme.colors.primary}66`; // 40% opacity
      if (isActive) return 'rgba(59, 130, 246, 0.4)';
      if (isOptimal) return 'rgba(16, 185, 129, 0.3)';
      return 'rgba(255, 255, 255, 0.1)';
    }};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
`;

const WindowHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const WindowLabel = styled.div<{ isActive: boolean }>`
  font-weight: 700;
  color: ${({ isActive, theme }) => (isActive ? theme.colors.primary : '#ffffff')};
  font-size: 14px;
`;

const WindowTime = styled.div`
  color: #9ca3af;
  font-size: 12px;
`;

const WindowStats = styled.div`
  display: flex;
  gap: 16px;
  font-size: 12px;
`;

const WindowStat = styled.div`
  color: #d1d5db;
`;

const LiveIndicator = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: ${({ theme }) => theme.colors.primary};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &::before {
    content: '';
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;

const ProbabilityCard = styled.div<{ recommendation: string }>`
  background: ${({ recommendation }) => {
    switch (recommendation) {
      case 'PRIORITIZE':
        return 'var(--recommendation-prioritize-bg)';
      case 'INCREASE_SIZE':
        return 'var(--recommendation-increase-bg)';
      case 'STANDARD':
        return 'var(--recommendation-standard-bg)';
      case 'REDUCE_SIZE':
        return 'var(--recommendation-reduce-bg)';
      default:
        return 'var(--recommendation-avoid-bg)';
    }
  }};
  border: 1px solid var(--setup-success-border);
  border-radius: 12px;
  padding: 20px;
  opacity: 0.9;
`;

const ProbabilityHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const ProbabilityValue = styled.div`
  font-size: 32px;
  font-weight: 800;
  color: #ffffff;
`;

const RecommendationBadge = styled.div<{ recommendation: string }>`
  background: ${({ recommendation }) => {
    switch (recommendation) {
      case 'PRIORITIZE':
        return 'var(--recommendation-prioritize-bg)';
      case 'INCREASE_SIZE':
        return 'var(--recommendation-increase-bg)';
      case 'STANDARD':
        return 'var(--recommendation-standard-bg)';
      case 'REDUCE_SIZE':
        return 'var(--recommendation-reduce-bg)';
      default:
        return 'var(--recommendation-avoid-bg)';
    }
  }};
  color: ${({ recommendation }) => {
    switch (recommendation) {
      case 'PRIORITIZE':
        return 'var(--recommendation-prioritize-text)';
      case 'INCREASE_SIZE':
        return 'var(--recommendation-increase-text)';
      case 'STANDARD':
        return 'var(--recommendation-standard-text)';
      case 'REDUCE_SIZE':
        return 'var(--recommendation-reduce-text)';
      default:
        return 'var(--recommendation-avoid-text)';
    }
  }};
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
`;

const RiskManagementGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-top: 16px;
`;

const RiskItem = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
`;

const ModelComparisonCard = styled.div<{ isRecommended: boolean }>`
  background: ${({ isRecommended }) =>
    isRecommended
      ? 'var(--model-card-bg)' // Uses theme CSS variable
      : 'rgba(255, 255, 255, 0.03)'};
  border: 1px solid
    ${({ isRecommended }) =>
      isRecommended
        ? 'var(--model-card-border)' // Uses theme CSS variable
        : 'rgba(255, 255, 255, 0.1)'};
  border-radius: 8px;
  padding: 12px;
  text-align: center;
`;

const ModelName = styled.div<{ isRecommended: boolean }>`
  font-size: 14px;
  font-weight: 600;
  color: ${({ isRecommended }) => (isRecommended ? 'var(--model-name-color)' : '#ffffff')};
  margin-bottom: 8px;
`;

const RiskLabel = styled.div`
  color: #9ca3af;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
`;

const RiskValue = styled.div`
  color: #ffffff;
  font-weight: 600;
`;

const PriorityCard = styled.div<{ priority: 'HIGH' | 'MEDIUM' | 'LOW' }>`
  background: ${({ priority }) => {
    switch (priority) {
      case 'HIGH':
        return 'var(--priority-high-bg)';
      case 'MEDIUM':
        return 'var(--priority-medium-bg)';
      default:
        return 'var(--priority-low-bg)';
    }
  }};
  border: 1px solid
    ${({ priority }) => {
      switch (priority) {
        case 'HIGH':
          return 'var(--priority-high-border)';
        case 'MEDIUM':
          return 'var(--priority-medium-border)';
        default:
          return 'var(--priority-low-border)';
      }
    }};
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
`;

const PriorityLabel = styled.span<{ priority: 'HIGH' | 'MEDIUM' | 'LOW' }>`
  color: ${({ priority }) => {
    switch (priority) {
      case 'HIGH':
        return 'var(--priority-high-text)';
      case 'MEDIUM':
        return 'var(--priority-medium-text)';
      default:
        return 'var(--priority-low-text)';
    }
  }};
  font-size: 12px;
  font-weight: bold;
`;

const LiquidityCard = styled.div`
  background: var(--setup-intelligence-bg);
  border: 1px solid var(--setup-intelligence-border);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
`;

const SetupIntelligenceContainer = styled.div`
  background: var(--setup-intelligence-bg);
  border: 1px solid var(--setup-intelligence-border);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;

/**
 * Elite ICT Intelligence Component
 */
export const EliteICTIntelligence: React.FC<EliteICTIntelligenceProps> = ({
  isLoading = false,
  error = null,
  onRefresh,
  className,
}) => {
  const {
    recommendation: modelRec,
    modelStats,
    isLoading: modelLoading,
    error: modelError,
  } = useModelSelectionEngine();
  const {
    analysis: qualityAnalysis,
    isLoading: qualityLoading,
    error: qualityError,
  } = usePatternQualityScoring();
  const {
    sessionAnalyses,
    liveGuidance,
    isLoading: sessionLoading,
    error: sessionError,
  } = useGranularSessionIntelligence();
  const {
    successProbability,
    isLoading: probabilityLoading,
    error: probabilityError,
  } = useSuccessProbabilityCalculator(modelRec, qualityAnalysis.currentScore);
  const {
    setupIntelligence,
    isLoading: setupLoading,
    error: setupError,
  } = useEnhancedSetupIntelligence();

  const loading =
    isLoading ||
    modelLoading ||
    qualityLoading ||
    sessionLoading ||
    probabilityLoading ||
    setupLoading;
  const displayError = error || modelError || qualityError || sessionError || setupError;

  // Loading state
  if (loading) {
    return (
      <Card title="🎯 Elite ICT Trading Intelligence">
        <div style={{ padding: '24px', textAlign: 'center' }}>
          Analyzing market conditions and generating intelligent recommendations...
        </div>
      </Card>
    );
  }

  // Error state
  if (displayError) {
    return (
      <Card title="🎯 Elite ICT Trading Intelligence">
        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>
          Error: {displayError}
          {onRefresh && (
            <button
              onClick={onRefresh}
              style={{
                marginLeft: '16px',
                padding: '8px 16px',
                background: '#f0f0f0',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Retry
            </button>
          )}
        </div>
      </Card>
    );
  }

  const activeSession = liveGuidance.activeSession;

  return (
    <Card
      title="🎯 Elite ICT Trading Intelligence"
      actions={
        onRefresh ? (
          <button
            onClick={onRefresh}
            style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }}
          >
            🔄 Refresh
          </button>
        ) : undefined
      }
      className={className}
    >
      <Container>
        {/* Model Selection Engine */}
        <Section>
          <SectionTitle>🎯 MODEL RECOMMENDATION ENGINE</SectionTitle>
          <ModelRecommendationCard>
            <ModelHeader>
              <RecommendedModel>{modelRec.recommendedModel}</RecommendedModel>
              <ProbabilityBadge confidence={modelRec.confidence}>
                {modelRec.probability.toFixed(0)}% - {modelRec.confidence}
              </ProbabilityBadge>
            </ModelHeader>

            <ConditionsGrid>
              <ConditionItem>
                <ConditionLabel>Volatility</ConditionLabel>
                <ConditionValue>
                  {modelRec.marketConditions.volatility.toUpperCase()}
                </ConditionValue>
              </ConditionItem>
              <ConditionItem>
                <ConditionLabel>Liquidity</ConditionLabel>
                <ConditionValue>
                  {modelRec.marketConditions.liquidityContext.toUpperCase()}
                </ConditionValue>
              </ConditionItem>
              <ConditionItem>
                <ConditionLabel>HTF Trend</ConditionLabel>
                <ConditionValue>{modelRec.marketConditions.htfTrend.toUpperCase()}</ConditionValue>
              </ConditionItem>
              <ConditionItem>
                <ConditionLabel>Previous Success</ConditionLabel>
                <ConditionValue>
                  {modelRec.marketConditions.previousSessionSuccess || 'NONE'}
                </ConditionValue>
              </ConditionItem>
            </ConditionsGrid>

            <ReasoningText>
              <strong>Reasoning:</strong> {modelRec.reasoning}
            </ReasoningText>
            <ReasoningText>
              <strong>Alternative:</strong> {modelRec.alternativeModel}{' '}
              {modelRec.alternativeCondition}
            </ReasoningText>
          </ModelRecommendationCard>
        </Section>

        {/* Model Performance Analysis */}
        <Section>
          <SectionTitle>📊 MODEL PERFORMANCE ANALYSIS</SectionTitle>
          <div style={{ display: 'grid', gap: '16px' }}>
            {/* RD-Cont vs FVG-RD Comparison */}
            <div
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                borderRadius: '12px',
                padding: '16px',
                border: '1px solid rgba(255, 255, 255, 0.1)',
              }}
            >
              <div
                style={{
                  fontSize: '14px',
                  color: '#9ca3af',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px',
                  marginBottom: '12px',
                }}
              >
                Model Performance Comparison
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <ModelComparisonCard isRecommended={modelRec.recommendedModel === 'RD-Cont'}>
                  <ModelName isRecommended={modelRec.recommendedModel === 'RD-Cont'}>
                    RD-Cont Model
                  </ModelName>
                  <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                    Historical Performance
                    <br />
                    Based on your trading data
                  </div>
                </ModelComparisonCard>

                <ModelComparisonCard isRecommended={modelRec.recommendedModel === 'FVG-RD'}>
                  <ModelName isRecommended={modelRec.recommendedModel === 'FVG-RD'}>
                    FVG-RD Model
                  </ModelName>
                  <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                    Historical Performance
                    <br />
                    Based on your trading data
                  </div>
                </ModelComparisonCard>
              </div>

              <div
                style={{
                  textAlign: 'center',
                  marginTop: '12px',
                  fontSize: '14px',
                  color: '#dc2626',
                  fontWeight: '600',
                }}
              >
                Current Recommendation: {modelRec.recommendedModel}
              </div>
            </div>
          </div>
        </Section>

        {/* Success Probability Calculator */}
        <Section>
          <SectionTitle>🎯 SETUP SUCCESS PROBABILITY</SectionTitle>
          <ProbabilityCard recommendation={successProbability.recommendation}>
            <ProbabilityHeader>
              <ProbabilityValue>{successProbability.finalProbability.toFixed(0)}%</ProbabilityValue>
              <RecommendationBadge recommendation={successProbability.recommendation}>
                {successProbability.recommendation.replace('_', ' ')}
              </RecommendationBadge>
            </ProbabilityHeader>

            <BreakdownGrid>
              <BreakdownItem>
                <BreakdownLabel>Base Model Win Rate</BreakdownLabel>
                <BreakdownScore>
                  {successProbability.breakdown.baseModelWinRate.toFixed(0)}%
                </BreakdownScore>
              </BreakdownItem>
              <BreakdownItem>
                <BreakdownLabel>Session Bonus</BreakdownLabel>
                <BreakdownScore>
                  {successProbability.breakdown.sessionBonus > 0 ? '+' : ''}
                  {successProbability.breakdown.sessionBonus.toFixed(0)}%
                </BreakdownScore>
              </BreakdownItem>
              <BreakdownItem>
                <BreakdownLabel>Quality Bonus</BreakdownLabel>
                <BreakdownScore>
                  {successProbability.breakdown.qualityBonus > 0 ? '+' : ''}
                  {successProbability.breakdown.qualityBonus.toFixed(0)}%
                </BreakdownScore>
              </BreakdownItem>
              <BreakdownItem>
                <BreakdownLabel>News Impact</BreakdownLabel>
                <BreakdownScore>
                  {successProbability.breakdown.newsImpact > 0 ? '+' : ''}
                  {successProbability.breakdown.newsImpact.toFixed(0)}%
                </BreakdownScore>
              </BreakdownItem>
            </BreakdownGrid>

            <ReasoningText>
              <strong>Expected R-Multiple:</strong>{' '}
              {successProbability.expectedRMultiple.min.toFixed(1)} -{' '}
              {successProbability.expectedRMultiple.max.toFixed(1)} (avg:{' '}
              {successProbability.expectedRMultiple.average.toFixed(1)})
            </ReasoningText>

            <RiskManagementGrid>
              <RiskItem>
                <RiskLabel>Position Sizing</RiskLabel>
                <RiskValue>{successProbability.riskManagement.positionSizing}</RiskValue>
              </RiskItem>
              <RiskItem>
                <RiskLabel>Max Risk</RiskLabel>
                <RiskValue>{successProbability.riskManagement.maxRiskPercent}%</RiskValue>
              </RiskItem>
              <RiskItem>
                <RiskLabel>Stop Loss</RiskLabel>
                <RiskValue>{successProbability.riskManagement.stopLossMultiplier}x</RiskValue>
              </RiskItem>
              <RiskItem>
                <RiskLabel>Take Profit</RiskLabel>
                <RiskValue>
                  {successProbability.riskManagement.takeProfitTargets.join('R, ')}R
                </RiskValue>
              </RiskItem>
            </RiskManagementGrid>
          </ProbabilityCard>
        </Section>

        <IntelligenceGrid>
          {/* Pattern Quality Scoring */}
          <Section>
            <SectionTitle>📊 PATTERN QUALITY ANALYSIS</SectionTitle>
            <PatternQualityCard rating={qualityAnalysis.currentScore.rating}>
              <QualityScore>
                <ScoreValue>{qualityAnalysis.currentScore.totalScore.toFixed(1)}</ScoreValue>
                <ScoreMax>/5.0</ScoreMax>
                <QualityRating rating={qualityAnalysis.currentScore.rating}>
                  {qualityAnalysis.currentScore.rating}
                </QualityRating>
              </QualityScore>

              <BreakdownGrid>
                <BreakdownItem>
                  <BreakdownLabel>PD Array Confluence</BreakdownLabel>
                  <BreakdownScore>
                    {qualityAnalysis.currentScore.breakdown.pdArrayConfluence.toFixed(1)}
                  </BreakdownScore>
                </BreakdownItem>
                <BreakdownItem>
                  <BreakdownLabel>FVG Characteristics</BreakdownLabel>
                  <BreakdownScore>
                    {qualityAnalysis.currentScore.breakdown.fvgCharacteristics.toFixed(1)}
                  </BreakdownScore>
                </BreakdownItem>
                <BreakdownItem>
                  <BreakdownLabel>RD Strength</BreakdownLabel>
                  <BreakdownScore>
                    {qualityAnalysis.currentScore.breakdown.rdStrength.toFixed(1)}
                  </BreakdownScore>
                </BreakdownItem>
                <BreakdownItem>
                  <BreakdownLabel>Confirmation</BreakdownLabel>
                  <BreakdownScore>
                    {qualityAnalysis.currentScore.breakdown.confirmationSignals.toFixed(1)}
                  </BreakdownScore>
                </BreakdownItem>
              </BreakdownGrid>

              <ReasoningText>
                <strong>Recommendation:</strong> {qualityAnalysis.currentScore.recommendation}
              </ReasoningText>
              <ReasoningText>
                <strong>Expected Win Probability:</strong>{' '}
                {qualityAnalysis.currentScore.expectedWinProbability.toFixed(0)}%
              </ReasoningText>
            </PatternQualityCard>
          </Section>

          {/* Setup Intelligence */}
          <Section>
            <SectionTitle>🎯 SETUP INTELLIGENCE</SectionTitle>

            {/* Current Recommendations */}
            <div style={{ marginBottom: '20px' }}>
              <h4 style={{ color: 'var(--text-primary)', marginBottom: '12px', fontSize: '16px' }}>
                Current Recommendations
              </h4>
              <SetupIntelligenceContainer>
                <div style={{ color: 'var(--text-primary)', marginBottom: '8px' }}>
                  <strong>Primary Setup:</strong>{' '}
                  {setupIntelligence.currentRecommendations.primarySetup}
                </div>
                <div style={{ color: 'var(--text-primary)', marginBottom: '8px' }}>
                  <strong>Secondary Setup:</strong>{' '}
                  {setupIntelligence.currentRecommendations.secondarySetup}
                </div>
                <div style={{ color: 'var(--text-primary)', marginBottom: '8px' }}>
                  <strong>Liquidity Target:</strong>{' '}
                  {setupIntelligence.currentRecommendations.liquidityTarget}
                </div>
                <div
                  style={{ color: 'var(--text-secondary)', fontSize: '14px', marginTop: '12px' }}
                >
                  {setupIntelligence.currentRecommendations.reasoning}
                </div>
                <div
                  style={{
                    display: 'flex',
                    gap: '16px',
                    marginTop: '12px',
                    fontSize: '14px',
                  }}
                >
                  <span style={{ color: 'var(--setup-intelligence-accent)' }}>
                    Expected Win Rate:{' '}
                    {setupIntelligence.currentRecommendations.expectedWinRate.toFixed(0)}%
                  </span>
                  <span style={{ color: 'var(--setup-intelligence-accent)' }}>
                    Expected R-Multiple:{' '}
                    {setupIntelligence.currentRecommendations.expectedRMultiple.toFixed(1)}
                  </span>
                </div>
              </SetupIntelligenceContainer>
            </div>

            {/* Best Setup Combinations */}
            {setupIntelligence.bestCombinations.length > 0 && (
              <div style={{ marginBottom: '20px' }}>
                <h4
                  style={{ color: 'var(--text-primary)', marginBottom: '12px', fontSize: '16px' }}
                >
                  Top Setup Combinations
                </h4>
                {setupIntelligence.bestCombinations.slice(0, 3).map((combo, index) => (
                  <PriorityCard key={index} priority={combo.priority}>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: '8px',
                      }}
                    >
                      <PriorityLabel priority={combo.priority}>
                        {combo.priority} PRIORITY
                      </PriorityLabel>
                      <div style={{ display: 'flex', gap: '12px', fontSize: '12px' }}>
                        <span style={{ color: 'var(--success-color)' }}>
                          {combo.performance.winRate.toFixed(0)}% Win Rate
                        </span>
                        <span style={{ color: 'var(--setup-intelligence-accent)' }}>
                          {combo.performance.avgRMultiple.toFixed(1)}R Avg
                        </span>
                        <span style={{ color: 'var(--text-muted)' }}>
                          {combo.performance.totalTrades} Trades
                        </span>
                      </div>
                    </div>
                    <div
                      style={{
                        color: 'var(--text-primary)',
                        fontSize: '14px',
                        marginBottom: '4px',
                      }}
                    >
                      <strong>{combo.primary}</strong> + <strong>{combo.secondary}</strong>
                    </div>
                    <div
                      style={{
                        color: 'var(--text-secondary)',
                        fontSize: '13px',
                        marginBottom: '8px',
                      }}
                    >
                      Liquidity: {combo.liquidity}
                    </div>
                    <div style={{ color: 'var(--text-secondary)', fontSize: '12px' }}>
                      {combo.recommendation}
                    </div>
                  </PriorityCard>
                ))}
              </div>
            )}

            {/* Liquidity Intelligence */}
            {setupIntelligence.liquidityIntelligence.length > 0 && (
              <div>
                <h4
                  style={{ color: 'var(--text-primary)', marginBottom: '12px', fontSize: '16px' }}
                >
                  Liquidity Target Intelligence
                </h4>
                {setupIntelligence.liquidityIntelligence.slice(0, 3).map((liquidity, index) => (
                  <LiquidityCard key={index}>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: '8px',
                      }}
                    >
                      <span style={{ color: 'var(--text-primary)', fontWeight: 'bold' }}>
                        {liquidity.liquidityTarget}
                      </span>
                      <div style={{ display: 'flex', gap: '12px', fontSize: '12px' }}>
                        <span style={{ color: 'var(--success-color)' }}>
                          {liquidity.performance.winRate.toFixed(0)}% Success
                        </span>
                        <span style={{ color: 'var(--setup-intelligence-accent)' }}>
                          {liquidity.performance.avgRMultiple.toFixed(1)}R Avg
                        </span>
                        <span style={{ color: 'var(--text-muted)' }}>
                          {liquidity.performance.totalTrades} Trades
                        </span>
                      </div>
                    </div>
                    {liquidity.bestModels.length > 0 && (
                      <div
                        style={{
                          color: 'var(--text-secondary)',
                          fontSize: '13px',
                          marginBottom: '4px',
                        }}
                      >
                        Best Models: {liquidity.bestModels.join(', ')}
                      </div>
                    )}
                    {liquidity.bestSessions.length > 0 && (
                      <div
                        style={{
                          color: 'var(--text-secondary)',
                          fontSize: '13px',
                          marginBottom: '8px',
                        }}
                      >
                        Best Sessions: {liquidity.bestSessions.join(', ')}
                      </div>
                    )}
                    <div style={{ color: 'var(--text-secondary)', fontSize: '12px' }}>
                      {liquidity.recommendation}
                    </div>
                  </LiquidityCard>
                ))}
              </div>
            )}
          </Section>
        </IntelligenceGrid>
      </Container>
    </Card>
  );
};
