/**
 * Enhanced Session Focus Component
 *
 * Advanced ICT session intelligence using real trading data from spreadsheet import.
 * Provides sophisticated PD Array and model-specific analysis with ADHD-optimized format.
 */

import React from 'react';
import styled from 'styled-components';
import { Card, DualTimeDisplay } from '@adhd-trading-dashboard/shared';
import {
  useSessionAnalytics,
  SessionPerformance,
  CurrentSessionRecommendation,
} from '../hooks/useSessionAnalytics';
import { useEnhancedSessionIntelligence } from '../hooks/useEnhancedSessionIntelligence';

export interface SessionFocusProps {
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when the refresh button is clicked */
  onRefresh?: () => void;
  /** Additional class name */
  className?: string;
}

// Styled components with F1 theme
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
`;

const CurrentSessionCard = styled.div.attrs({
  className: 'session-card current-session-card',
})`
  /* Use CSS variables for theming */
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-accent);
  border-left: 4px solid var(--session-card-accent);
  border-radius: 12px;
  padding: 20px;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md), 0 0 0 1px var(--session-card-accent);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(
      90deg,
      var(--session-card-accent),
      var(--session-card-active-accent),
      var(--session-card-accent)
    );
    animation: pulse 2s ease-in-out infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }
`;

const SessionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const SessionTitle = styled.h3`
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const LiveIndicatorSpan = styled.span`
  background: ${({ theme }) =>
    `linear-gradient(135deg, ${theme.colors.sessionActive}, ${theme.colors.sessionOptimal})`};
  color: ${({ theme }) => theme.colors.textPrimary};
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  animation: f1Pulse 2s ease-in-out infinite;
  box-shadow: 0 0 8px ${({ theme }) => theme.colors.sessionActive}40;

  @keyframes f1Pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }
`;

const RecommendationBadge = styled.div<{ level: CurrentSessionRecommendation['recommendation'] }>`
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: ${({ level, theme }) => {
    switch (level) {
      case 'high':
        // F1 Racing: Green flag for high recommendation
        return `linear-gradient(135deg, ${theme.colors.sessionActive}, ${theme.colors.sessionOptimal})`;
      case 'medium':
        // F1 Racing: Yellow flag for caution/medium
        return `linear-gradient(135deg, ${theme.colors.sessionCaution}, ${theme.colors.sessionTransition})`;
      case 'low':
        // F1 Racing: Orange flag for poor conditions
        return `linear-gradient(135deg, ${theme.colors.sessionTransition}, ${theme.colors.performancePoor})`;
      case 'avoid':
        // F1 Racing: Red flag for danger/avoid
        return `linear-gradient(135deg, ${theme.colors.performanceAvoid}, ${theme.colors.error})`;
      default:
        return 'linear-gradient(135deg, #6b7280, #4b5563)';
    }
  }};

  /* F1 Racing: Add racing stripe for high recommendations */
  ${({ level }) =>
    level === 'high' &&
    `
    position: relative;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 3px;
      background: linear-gradient(180deg, #00FFE5, #00D2BE);
      border-radius: 6px 0 0 6px;
    }
  `}
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const MetricCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
`;

const MetricValue = styled.div`
  font-size: 20px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin-bottom: 4px;
`;

const MetricLabel = styled.div`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.textSecondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ActionItems = styled.div`
  background: ${({ theme }) =>
    `linear-gradient(135deg, ${theme.colors.sessionActive}20, ${theme.colors.sessionOptimal}10)`};
  border: 2px solid ${({ theme }) => theme.colors.sessionActive};
  border-left: 6px solid ${({ theme }) => theme.colors.sessionActive};
  border-radius: 8px;
  padding: 16px;
  position: relative;

  /* Mercedes F1 racing stripe accent */
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: linear-gradient(
      180deg,
      ${({ theme }) => theme.colors.sessionOptimal},
      ${({ theme }) => theme.colors.sessionActive}
    );
    border-radius: 8px 0 0 8px;
  }
`;

const ActionTitle = styled.h4`
  color: ${({ theme }) => theme.colors.sessionActive};
  font-size: 14px;
  font-weight: 700;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 0 4px ${({ theme }) => theme.colors.sessionActive}40;
`;

const ActionList = styled.ul`
  margin: 0;
  padding: 0;
  list-style: none;
`;

const ActionItem = styled.li`
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: 14px;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;

  &::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: ${({ theme }) => theme.colors.sessionActive};
    font-size: 12px;
    text-shadow: 0 0 4px ${({ theme }) => theme.colors.sessionActive}40;
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

const SessionGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
  margin-top: 16px;
`;

const SessionBlock = styled.div<{
  performance: SessionPerformance['performance'];
  isCurrent: boolean;
}>`
  background: ${({ performance, isCurrent, theme }) => {
    // F1 Racing: Active session uses Mercedes Green (green flag)
    if (isCurrent)
      return `linear-gradient(135deg, ${theme.colors.sessionActive}, ${theme.colors.sessionOptimal})`;
    switch (performance) {
      case 'excellent':
        return `linear-gradient(135deg, ${theme.colors.performanceExcellent}, ${theme.colors.sessionOptimal})`;
      case 'good':
        return `linear-gradient(135deg, ${theme.colors.performanceGood}, ${theme.colors.secondary})`;
      case 'average':
        return `linear-gradient(135deg, ${theme.colors.performanceAverage}, #4b5563)`;
      case 'poor':
        return `linear-gradient(135deg, ${theme.colors.performancePoor}, ${theme.colors.sessionTransition})`;
      case 'avoid':
        return `linear-gradient(135deg, ${theme.colors.performanceAvoid}, ${theme.colors.error})`;
      default:
        return 'linear-gradient(135deg, #374151, #1f2937)';
    }
  }};
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  position: relative;
  ${({ isCurrent, theme }) =>
    isCurrent &&
    `
    border: 2px solid ${theme.colors.sessionOptimal};
    box-shadow: 0 0 15px rgba(0, 210, 190, 0.6);
    position: relative;

    /* F1 Racing stripe accent */
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 3px;
      background: linear-gradient(180deg, ${theme.colors.sessionOptimal}, ${theme.colors.sessionActive});
      border-radius: 6px 0 0 6px;
    }
  `}
`;

const SessionHour = styled.div`
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
`;

const SessionWinRate = styled.div`
  font-size: 14px;
  font-weight: 700;
  color: #ffffff;
`;

const SessionLabel = styled.div`
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
`;

// Enhanced ICT Session Components with F1 Racing Theme
const ICTSessionCard = styled.div.attrs<{ isActive: boolean; isOptimal: boolean }>(
  ({ isActive, isOptimal }) => ({
    className: `session-card ict-session-card ${isActive ? 'active' : ''} ${
      isOptimal ? 'optimal' : ''
    }`,
    'data-session-state':
      isActive && isOptimal ? 'active-optimal' : isActive ? 'active' : 'inactive',
  })
)<{ isActive: boolean; isOptimal: boolean }>`
  /* Use CSS variables for clean theming */
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-left: 4px solid var(--session-card-accent);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);

  &[data-session-state='active'] {
    border-left-color: var(--session-active);
    border-color: var(--session-active);
    box-shadow: var(--shadow-md), 0 0 0 1px var(--session-active);
  }

  &[data-session-state='active-optimal'] {
    border-left-color: var(--session-optimal);
    border-color: var(--session-optimal);
    box-shadow: var(--shadow-md), 0 0 0 1px var(--session-optimal), var(--shadow-accent);
  }
`;

const ICTSessionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const ICTSessionName = styled.div<{ isActive: boolean }>`
  font-size: 16px;
  font-weight: 700;
  color: ${({ isActive }) => (isActive ? 'var(--session-active)' : 'var(--session-text-primary)')};
  display: flex;
  align-items: center;
  gap: 8px;
  text-shadow: none;
`;

const ICTTimeRange = styled.div`
  font-size: 12px;
  color: var(--session-text-secondary);
`;

const ICTPerformanceGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin: 12px 0;
`;

const ICTMetric = styled.div`
  text-align: center;
`;

const ICTMetricValue = styled.div`
  font-size: 18px;
  font-weight: 700;
  color: var(--session-text-primary);
`;

const ICTMetricLabel = styled.div`
  font-size: 10px;
  color: var(--session-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ModelPreferenceCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
`;

const ModelPreferenceHeader = styled.div`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.textSecondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
`;

const ModelComparison = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
`;

const ModelStats = styled.div<{ isRecommended: boolean }>`
  background: ${({ isRecommended }) =>
    isRecommended ? 'var(--active-overlay)' : 'var(--hover-overlay)'};
  border: 1px solid
    ${({ isRecommended }) =>
      isRecommended ? 'var(--session-active)' : 'var(--session-card-border)'};
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  position: relative;
  transition: all 0.2s ease;

  ${({ isRecommended }) =>
    isRecommended &&
    `
    box-shadow: 0 0 0 1px var(--session-active);

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 12px;
      height: 12px;
      background: var(--session-active);
      border-radius: 0 6px 0 6px;
    }
  `}
`;

const ModelName = styled.div<{ isRecommended: boolean }>`
  font-size: 12px;
  font-weight: 600;
  color: ${({ isRecommended, theme }) =>
    isRecommended ? theme.colors.sessionActive : theme.colors.textPrimary};
  margin-bottom: 4px;
`;

const ModelMetrics = styled.div`
  font-size: 10px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const OptimalWindowsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-top: 8px;
`;

const WindowCard = styled.div.attrs<{ isOptimal: boolean }>(({ isOptimal }) => ({
  className: `window-card optimal-window-card ${isOptimal ? 'optimal' : ''}`,
}))<{ isOptimal: boolean }>`
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);

  ${({ isOptimal }) =>
    isOptimal &&
    `
    border-left: 3px solid var(--session-optimal);
    border-color: var(--session-optimal);
    box-shadow:
      var(--shadow-sm),
      0 0 0 1px var(--session-optimal);
  `}
`;

const WindowTime = styled.div.attrs({
  className: 'primary-text session-title',
})`
  font-size: 11px;
  font-weight: 600;
  color: var(--session-text-primary);
`;

const WindowDescription = styled.div.attrs({
  className: 'secondary-text session-description',
})`
  font-size: 9px;
  color: var(--session-text-secondary);
  margin: 2px 0;
`;

const WindowStats = styled.div.attrs({
  className: 'secondary-text metric-label',
})`
  font-size: 9px;
  color: var(--session-text-secondary);
`;

const LiveIndicator = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, var(--session-active), var(--session-optimal));
  color: var(--session-text-primary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-accent);

  &::before {
    content: '';
    width: 6px;
    height: 6px;
    background: var(--session-text-primary);
    border-radius: 50%;
    animation: mercedesPulse 2s infinite;
  }

  @keyframes mercedesPulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.2);
    }
  }
`;

const WeeklyInsightsCard = styled.div`
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(147, 51, 234, 0.05));
  border: 1px solid rgba(168, 85, 247, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
`;

const InsightsHeader = styled.div`
  font-size: 14px;
  font-weight: 700;
  color: #a855f7;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const InsightsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const InsightItem = styled.div`
  font-size: 12px;
  color: #d1d5db;
  padding: 6px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &:last-child {
    border-bottom: none;
  }
`;

/**
 * Session Focus Component
 */
export const SessionFocus: React.FC<SessionFocusProps> = ({
  isLoading = false,
  error = null,
  onRefresh,
  className,
}) => {
  const { analytics, isLoading: analyticsLoading, error: analyticsError } = useSessionAnalytics();
  const {
    intelligence,
    isLoading: enhancedLoading,
    error: enhancedError,
  } = useEnhancedSessionIntelligence();

  const loading = isLoading || analyticsLoading || enhancedLoading;
  const displayError = error || analyticsError || enhancedError;

  // Loading state
  if (loading) {
    return (
      <Card title="🎯 Session Focus">
        <div style={{ padding: '24px', textAlign: 'center' }}>
          Analyzing your trading sessions...
        </div>
      </Card>
    );
  }

  // Error state
  if (displayError) {
    return (
      <Card title="🎯 Session Focus">
        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>
          Error: {displayError}
          {onRefresh && (
            <button
              onClick={onRefresh}
              style={{
                marginLeft: '16px',
                padding: '8px 16px',
                background: '#f0f0f0',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Retry
            </button>
          )}
        </div>
      </Card>
    );
  }

  const { currentRecommendation, sessionPerformance, totalAnalyzedTrades } = analytics;

  return (
    <Card
      title="🕘 Enhanced Session Intelligence"
      actions={
        onRefresh ? (
          <button
            onClick={onRefresh}
            style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }}
          >
            🔄 Refresh
          </button>
        ) : undefined
      }
    >
      <Container className={className}>
        {/* Current Session Status */}
        <CurrentSessionCard>
          <SessionHeader>
            <SessionTitle>
              {intelligence.currentStatus.currentRecommendation}
              {intelligence.currentStatus.activeSession && (
                <LiveIndicatorSpan>LIVE</LiveIndicatorSpan>
              )}
            </SessionTitle>
            <RecommendationBadge
              level={
                intelligence.currentStatus.urgency.toLowerCase() as
                  | 'high'
                  | 'medium'
                  | 'low'
                  | 'avoid'
              }
            >
              {intelligence.currentStatus.urgency}
            </RecommendationBadge>
          </SessionHeader>

          <MetricsGrid>
            <MetricCard>
              <MetricValue>
                <DualTimeDisplay mode="current" format="compact" showLive={true} />
              </MetricValue>
              <MetricLabel>Current Time</MetricLabel>
            </MetricCard>
            <MetricCard>
              <MetricValue>
                {intelligence.currentStatus.activeSession?.sessionName || 'None'}
              </MetricValue>
              <MetricLabel>Active Session</MetricLabel>
            </MetricCard>
            <MetricCard>
              <MetricValue>{intelligence.currentStatus.timeToNextFormatted}</MetricValue>
              <MetricLabel>Next Session</MetricLabel>
            </MetricCard>
          </MetricsGrid>
        </CurrentSessionCard>

        {/* Live Session Intelligence */}
        {intelligence.currentStatus.activeSession && (
          <ICTSessionCard isActive={true} isOptimal={true}>
            <ICTSessionHeader>
              <ICTSessionName isActive={true}>
                ⏰ LIVE SESSION INTELLIGENCE
                <LiveIndicatorSpan>LIVE SESSION</LiveIndicatorSpan>
              </ICTSessionName>
            </ICTSessionHeader>

            <div style={{ marginBottom: '16px' }}>
              <h4 style={{ color: '#ffffff', marginBottom: '12px', fontSize: '16px' }}>
                {intelligence.currentStatus.activeSession.sessionName} - Active
              </h4>

              {/* Session Windows Display */}
              <div style={{ display: 'grid', gap: '12px' }}>
                {intelligence.sessions
                  .find(
                    (s) => s.sessionName === intelligence.currentStatus.activeSession?.sessionName
                  )
                  ?.optimalWindows.map((window, index) => (
                    <WindowCard key={index} isOptimal={window.winRate >= 70 && window.trades >= 2}>
                      <WindowTime>
                        <DualTimeDisplay
                          mode="session"
                          sessionStart={window.start}
                          sessionEnd={window.end}
                          format="compact"
                        />
                      </WindowTime>
                      <WindowDescription>{window.description}</WindowDescription>
                      <WindowStats>
                        {window.trades > 0
                          ? `${window.winRate.toFixed(0)}% win rate (${window.trades} trades)`
                          : 'No data'}
                      </WindowStats>
                    </WindowCard>
                  ))}
              </div>
            </div>
          </ICTSessionCard>
        )}

        {/* ICT Session Analysis */}
        {intelligence.sessions.map((session) => {
          const isActive =
            intelligence.currentStatus.activeSession?.sessionName === session.sessionName;
          const hasOptimalWindow = session.optimalWindows.some((w) => w.winRate >= 70);

          return (
            <ICTSessionCard
              key={session.sessionName}
              isActive={isActive}
              isOptimal={hasOptimalWindow}
            >
              <ICTSessionHeader>
                <ICTSessionName isActive={isActive}>
                  {session.sessionName}
                  {isActive && <LiveIndicatorSpan>ACTIVE</LiveIndicatorSpan>}
                </ICTSessionName>
                <ICTTimeRange>
                  {session.timeRange ? (
                    <DualTimeDisplay
                      mode="session"
                      sessionStart={session.timeRange.split('-')[0]}
                      sessionEnd={session.timeRange.split('-')[1]}
                      format="compact"
                    />
                  ) : (
                    <span>Time range not available</span>
                  )}
                </ICTTimeRange>
              </ICTSessionHeader>

              <ICTPerformanceGrid>
                <ICTMetric>
                  <ICTMetricValue>{session.performance.totalTrades}</ICTMetricValue>
                  <ICTMetricLabel>Trades</ICTMetricLabel>
                </ICTMetric>
                <ICTMetric>
                  <ICTMetricValue>{session.performance.winRate.toFixed(0)}%</ICTMetricValue>
                  <ICTMetricLabel>Win Rate</ICTMetricLabel>
                </ICTMetric>
                <ICTMetric>
                  <ICTMetricValue>{session.performance.avgRMultiple.toFixed(1)}R</ICTMetricValue>
                  <ICTMetricLabel>Avg R-Multiple</ICTMetricLabel>
                </ICTMetric>
                <ICTMetric>
                  <ICTMetricValue>{session.performance.avgRisk.toFixed(0)}</ICTMetricValue>
                  <ICTMetricLabel>Avg Risk (pts)</ICTMetricLabel>
                </ICTMetric>
              </ICTPerformanceGrid>

              {/* Optimal Windows */}
              <div>
                <ModelPreferenceHeader>Optimal Time Windows</ModelPreferenceHeader>
                <OptimalWindowsGrid>
                  {session.optimalWindows.map((window, index) => (
                    <WindowCard key={index} isOptimal={window.winRate >= 70 && window.trades >= 2}>
                      <WindowTime>
                        <DualTimeDisplay
                          mode="session"
                          sessionStart={window.start}
                          sessionEnd={window.end}
                          format="compact"
                        />
                      </WindowTime>
                      <WindowDescription>{window.description}</WindowDescription>
                      <WindowStats>
                        {window.trades > 0
                          ? `${window.winRate.toFixed(0)}% (${window.trades} trades)`
                          : 'No data'}
                      </WindowStats>
                    </WindowCard>
                  ))}
                </OptimalWindowsGrid>
              </div>

              {/* Session Recommendations */}
              {session.recommendations.length > 0 && (
                <ActionItems>
                  <ActionTitle>Session Recommendations</ActionTitle>
                  <ActionList>
                    {session.recommendations.map((rec, index) => (
                      <ActionItem key={index}>{rec}</ActionItem>
                    ))}
                  </ActionList>
                </ActionItems>
              )}
            </ICTSessionCard>
          );
        })}

        {/* Weekly Insights */}
        <WeeklyInsightsCard>
          <InsightsHeader>📊 Weekly Performance Insights</InsightsHeader>
          <InsightsList>
            <InsightItem>
              <strong>Best Session:</strong> {intelligence.weeklyInsights.bestSession}
            </InsightItem>
            <InsightItem>
              <strong>Best Model:</strong> {intelligence.weeklyInsights.bestModel}
            </InsightItem>
            <InsightItem>
              <strong>Average Quality:</strong> {intelligence.weeklyInsights.avgQuality.toFixed(1)}
              /5.0
            </InsightItem>
            <InsightItem>
              <strong>Quality Threshold:</strong>{' '}
              {`>${intelligence.weeklyInsights.qualityThreshold.toFixed(1)}`} for optimal
              performance
            </InsightItem>
          </InsightsList>
          <ActionItems style={{ marginTop: '12px' }}>
            <ActionTitle>Key Recommendations</ActionTitle>
            <ActionList>
              {intelligence.weeklyInsights.recommendations.map((rec, index) => (
                <ActionItem key={index}>{rec}</ActionItem>
              ))}
            </ActionList>
          </ActionItems>
        </WeeklyInsightsCard>
      </Container>
    </Card>
  );
};
