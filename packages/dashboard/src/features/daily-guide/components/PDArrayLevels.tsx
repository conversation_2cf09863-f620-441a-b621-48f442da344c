/**
 * PD Array Levels Component
 *
 * Replaces generic Key Levels with sophisticated ICT PD Array intelligence.
 * Tracks active FVGs, RD levels, liquidity targets, and parent PD arrays using real trading data.
 */

import React from 'react';
import styled from 'styled-components';
import { Card } from '@adhd-trading-dashboard/shared';
import { usePDArrayIntelligence } from '../hooks/usePDArrayIntelligence';

export interface PDArrayLevelsProps {
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when the refresh button is clicked */
  onRefresh?: () => void;
  /** Additional class name */
  className?: string;
}

// Styled components with F1 theme
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const SectionTitle = styled.h3`
  color: #ffffff;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PDArrayCard = styled.div.attrs<{ arrayType: string; isActive: boolean }>(
  ({ arrayType, isActive }) => ({
    className: `pd-array-card PDArrayCard ${isActive ? 'active' : ''}`,
    'data-array-type': arrayType,
    'data-active': isActive,
  })
)<{ arrayType: string; isActive: boolean }>`
  background: ${({ arrayType, isActive }) => {
    if (!isActive) return 'rgba(255, 255, 255, 0.03)';
    switch (arrayType) {
      case 'FVG':
        return 'linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1))';
      case 'NWOG':
        return 'linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.1))';
      case 'NDOG':
        return 'linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.1))';
      case 'RD':
        return 'linear-gradient(135deg, rgba(220, 38, 38, 0.2), rgba(239, 68, 68, 0.1))';
      case 'Liquidity':
        return 'linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(147, 51, 234, 0.1))';
      default:
        return 'rgba(255, 255, 255, 0.05)';
    }
  }};
  border: 1px solid
    ${({ arrayType, isActive }) => {
      if (!isActive) return 'rgba(255, 255, 255, 0.1)';
      switch (arrayType) {
        case 'FVG':
          return 'rgba(59, 130, 246, 0.4)';
        case 'NWOG':
          return 'rgba(16, 185, 129, 0.4)';
        case 'NDOG':
          return 'rgba(245, 158, 11, 0.4)';
        case 'RD':
          return 'rgba(220, 38, 38, 0.4)';
        case 'Liquidity':
          return 'rgba(168, 85, 247, 0.4)';
        default:
          return 'rgba(255, 255, 255, 0.2)';
      }
    }};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
`;

const ArrayHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const ArrayType = styled.div.attrs<{ arrayType: string }>(({ arrayType }) => ({
  className: `array-type ArrayType`,
  'data-array-type': arrayType,
}))<{ arrayType: string }>`
  font-size: 16px;
  font-weight: 700;
  color: ${({ arrayType }) => {
    switch (arrayType) {
      case 'FVG':
        return '#3b82f6';
      case 'NWOG':
        return '#10b981';
      case 'NDOG':
        return '#f59e0b';
      case 'RD':
        return '#dc2626';
      case 'Liquidity':
        return '#a855f7';
      default:
        return '#ffffff';
    }
  }};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ArrayStatus = styled.div.attrs<{ isActive: boolean }>(({ isActive }) => ({
  className: `array-status ArrayStatus`,
  'data-active': isActive,
}))<{ isActive: boolean }>`
  background: ${({ isActive }) => (isActive ? '#10b981' : '#6b7280')};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
`;

const LevelInfo = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin: 12px 0;
`;

const LevelDetail = styled.div`
  text-align: center;
`;

const LevelValue = styled.div`
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
`;

const LevelLabel = styled.div`
  font-size: 10px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2px;
`;

const PerformanceStats = styled.div.attrs({
  className: 'performance-stats PerformanceStats',
})`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
`;

const StatLabel = styled.div`
  font-size: 9px;
  color: #9ca3af;
  text-transform: uppercase;
`;

const PriorityBadge = styled.div<{ priority: 'HIGH' | 'MEDIUM' | 'LOW' }>`
  background: ${({ priority }) => {
    switch (priority) {
      case 'HIGH':
        return '#dc2626';
      case 'MEDIUM':
        return '#f59e0b';
      default:
        return '#6b7280';
    }
  }};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: 8px;
`;

const RecommendationText = styled.div`
  color: #d1d5db;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const EmptyTitle = styled.h3`
  color: #ffffff;
  font-size: 18px;
  margin-bottom: 8px;
`;

const EmptyMessage = styled.p`
  color: #9ca3af;
  font-size: 14px;
  line-height: 1.5;
`;

/**
 * PD Array Levels Component
 */
export const PDArrayLevels: React.FC<PDArrayLevelsProps> = ({
  isLoading = false,
  error = null,
  onRefresh,
  className,
}) => {
  const {
    pdArrayIntelligence,
    isLoading: intelligenceLoading,
    error: intelligenceError,
  } = usePDArrayIntelligence();

  const loading = isLoading || intelligenceLoading;
  const displayError = error || intelligenceError;

  // Loading state
  if (loading) {
    return (
      <Card title="🎯 PD Array Intelligence">
        <div style={{ padding: '24px', textAlign: 'center' }}>
          Analyzing PD Array levels and liquidity targets...
        </div>
      </Card>
    );
  }

  // Error state
  if (displayError) {
    return (
      <Card title="🎯 PD Array Intelligence">
        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>
          Error: {displayError}
          {onRefresh && (
            <button
              onClick={onRefresh}
              style={{
                marginLeft: '16px',
                padding: '8px 16px',
                background: '#f0f0f0',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Retry
            </button>
          )}
        </div>
      </Card>
    );
  }

  // Empty state
  if (!pdArrayIntelligence || pdArrayIntelligence.activePDArrays.length === 0) {
    return (
      <Card title="🎯 PD Array Intelligence">
        <EmptyState>
          <EmptyIcon>📊</EmptyIcon>
          <EmptyTitle>No PD Array Data Available</EmptyTitle>
          <EmptyMessage>
            Import your trading data to begin tracking FVGs, NWOG/NDOG levels, RD formations, and
            liquidity targets. The system will analyze your historical performance with each PD
            Array type.
          </EmptyMessage>
        </EmptyState>
      </Card>
    );
  }

  return (
    <Card
      title="🎯 PD Array Intelligence"
      actions={
        onRefresh ? (
          <button
            onClick={onRefresh}
            style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }}
          >
            🔄 Refresh
          </button>
        ) : undefined
      }
    >
      <Container className={className}>
        {/* Active PD Arrays */}
        <div>
          <SectionTitle>
            🔥 Active PD Arrays
            <PriorityBadge priority="HIGH">PRIORITY TARGETS</PriorityBadge>
          </SectionTitle>

          {pdArrayIntelligence.activePDArrays.map((array, index) => (
            <PDArrayCard key={index} arrayType={array.type} isActive={array.isActive}>
              <ArrayHeader>
                <ArrayType arrayType={array.type}>{array.type}</ArrayType>
                <ArrayStatus isActive={array.isActive}>
                  {array.isActive ? 'ACTIVE' : 'INACTIVE'}
                </ArrayStatus>
              </ArrayHeader>

              <LevelInfo>
                <LevelDetail>
                  <LevelValue>{array.level}</LevelValue>
                  <LevelLabel>Level</LevelLabel>
                </LevelDetail>
                <LevelDetail>
                  <LevelValue>{array.timeframe}</LevelValue>
                  <LevelLabel>Timeframe</LevelLabel>
                </LevelDetail>
                <LevelDetail>
                  <LevelValue>{array.age}</LevelValue>
                  <LevelLabel>Age</LevelLabel>
                </LevelDetail>
              </LevelInfo>

              <PerformanceStats>
                <StatsGrid>
                  <StatItem>
                    <StatValue>{array.performance.totalTrades}</StatValue>
                    <StatLabel>Trades</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatValue>{array.performance.winRate.toFixed(0)}%</StatValue>
                    <StatLabel>Win Rate</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatValue>{array.performance.avgRMultiple.toFixed(1)}R</StatValue>
                    <StatLabel>Avg R</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatValue>{array.performance.successRate.toFixed(0)}%</StatValue>
                    <StatLabel>Success Rate</StatLabel>
                  </StatItem>
                </StatsGrid>
              </PerformanceStats>

              <RecommendationText>
                <strong>Strategy:</strong> {array.recommendation}
              </RecommendationText>
            </PDArrayCard>
          ))}
        </div>

        {/* Summary Statistics */}
        <div>
          <SectionTitle>📊 PD Array Performance Summary</SectionTitle>
          <PDArrayCard arrayType="summary" isActive={true}>
            <StatsGrid>
              <StatItem>
                <StatValue>{pdArrayIntelligence.summary.totalActiveLevels}</StatValue>
                <StatLabel>Active Levels</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{pdArrayIntelligence.summary.bestPerformingType}</StatValue>
                <StatLabel>Best Type</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{pdArrayIntelligence.summary.overallSuccessRate.toFixed(0)}%</StatValue>
                <StatLabel>Overall Success</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{pdArrayIntelligence.summary.avgRMultiple.toFixed(1)}R</StatValue>
                <StatLabel>Avg R-Multiple</StatLabel>
              </StatItem>
            </StatsGrid>

            <RecommendationText>
              <strong>Key Insights:</strong> {pdArrayIntelligence.summary.keyInsights.join(' • ')}
            </RecommendationText>
          </PDArrayCard>
        </div>
      </Container>
    </Card>
  );
};
