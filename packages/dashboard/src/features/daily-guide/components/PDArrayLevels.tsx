/**
 * PD Array Levels Component
 *
 * Replaces generic Key Levels with sophisticated ICT PD Array intelligence.
 * Tracks active FVGs, RD levels, liquidity targets, and parent PD arrays using real trading data.
 */

import React from 'react';
import styled from 'styled-components';
import { Card } from '@adhd-trading-dashboard/shared';
import { usePDArrayIntelligence } from '../hooks/usePDArrayIntelligence';

export interface PDArrayLevelsProps {
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when the refresh button is clicked */
  onRefresh?: () => void;
  /** Additional class name */
  className?: string;
}

// Styled components with F1 theme
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const SectionTitle = styled.h3`
  color: #ffffff;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PDArrayCard = styled.div.attrs<{ arrayType: string; isActive: boolean }>(
  ({ arrayType, isActive }) => ({
    className: `pd-array-card PDArrayCard ${isActive ? 'active' : ''}`,
    'data-array-type': arrayType.toLowerCase(),
    'data-active': isActive,
  })
)<{ arrayType: string; isActive: boolean }>`
  /* Use CSS variables for clean theming */
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-left: 4px solid var(--session-card-accent);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);

  &[data-active='true'] {
    border-left-color: var(--pd-array-accent, var(--session-card-accent));
    box-shadow: var(--shadow-sm), 0 0 0 1px var(--pd-array-accent, var(--session-card-accent));
  }

  /* Array type styling handled by CSS variables */
  &[data-array-type='fvg'][data-active='true'] {
    --pd-array-accent: var(--info-color);
  }

  &[data-array-type='nwog'][data-active='true'] {
    --pd-array-accent: var(--success-color);
  }

  &[data-array-type='ndog'][data-active='true'] {
    --pd-array-accent: var(--warning-color);
  }

  &[data-array-type='rd'][data-active='true'] {
    --pd-array-accent: var(--error-color);
  }

  &[data-array-type='liquidity'][data-active='true'] {
    --pd-array-accent: var(--secondary-color);
  }

  &[data-array-type='summary'][data-active='true'] {
    --pd-array-accent: var(--primary-color);
  }
`;

const ArrayHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const ArrayType = styled.div.attrs<{ arrayType: string }>(({ arrayType }) => ({
  className: `array-type ArrayType`,
  'data-array-type': arrayType.toLowerCase(),
}))<{ arrayType: string }>`
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  /* Use CSS variables for array type colors */
  &[data-array-type='fvg'] {
    color: var(--info-color);
  }

  &[data-array-type='nwog'] {
    color: var(--success-color);
  }

  &[data-array-type='ndog'] {
    color: var(--warning-color);
  }

  &[data-array-type='rd'] {
    color: var(--error-color);
  }

  &[data-array-type='liquidity'] {
    color: var(--secondary-color);
  }

  &[data-array-type='summary'] {
    color: var(--primary-color);
  }

  /* Default color */
  color: var(--session-text-primary);
`;

const ArrayStatus = styled.div.attrs<{ isActive: boolean }>(({ isActive }) => ({
  className: `array-status ArrayStatus`,
  'data-active': isActive,
}))<{ isActive: boolean }>`
  background: ${({ isActive }) =>
    isActive ? 'var(--success-color)' : 'var(--session-card-border)'};
  color: ${({ isActive }) =>
    isActive ? 'var(--session-text-primary)' : 'var(--session-text-secondary)'};
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
`;

const LevelInfo = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin: 12px 0;
`;

const LevelDetail = styled.div`
  text-align: center;
`;

const LevelValue = styled.div`
  font-size: 16px;
  font-weight: 700;
  color: var(--session-text-primary);
`;

const LevelLabel = styled.div`
  font-size: 10px;
  color: var(--session-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2px;
`;

const PerformanceStats = styled.div.attrs({
  className: 'performance-stats PerformanceStats',
})`
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
  box-shadow: var(--shadow-sm);
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 14px;
  font-weight: 600;
  color: var(--session-text-primary);
`;

const StatLabel = styled.div`
  font-size: 9px;
  color: var(--session-text-secondary);
  text-transform: uppercase;
`;

const PriorityBadge = styled.div.attrs<{ priority: 'HIGH' | 'MEDIUM' | 'LOW' }>(({ priority }) => ({
  className: 'priority-badge',
  'data-priority': priority,
}))<{ priority: 'HIGH' | 'MEDIUM' | 'LOW' }>`
  background: ${({ priority }) => {
    switch (priority) {
      case 'HIGH':
        return 'var(--error-color)';
      case 'MEDIUM':
        return 'var(--warning-color)';
      default:
        return 'var(--session-card-border)';
    }
  }};
  color: ${({ priority }) =>
    priority === 'LOW' ? 'var(--session-text-secondary)' : 'var(--session-text-primary)'};
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: 8px;
`;

const RecommendationText = styled.div`
  color: var(--session-text-secondary);
  font-size: 12px;
  line-height: 1.4;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--session-card-border);
`;

const EmptyState = styled.div`
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-radius: 12px;
  text-align: center;
  padding: 40px 20px;
  color: var(--session-text-secondary);
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const EmptyTitle = styled.h3`
  color: var(--session-text-primary);
  font-size: 18px;
  margin-bottom: 8px;
`;

const EmptyMessage = styled.p`
  color: var(--session-text-secondary);
  font-size: 14px;
  line-height: 1.5;
`;

/**
 * PD Array Levels Component
 */
export const PDArrayLevels: React.FC<PDArrayLevelsProps> = ({
  isLoading = false,
  error = null,
  onRefresh,
  className,
}) => {
  const {
    pdArrayIntelligence,
    isLoading: intelligenceLoading,
    error: intelligenceError,
  } = usePDArrayIntelligence();

  const loading = isLoading || intelligenceLoading;
  const displayError = error || intelligenceError;

  // Loading state
  if (loading) {
    return (
      <Card title="🎯 PD Array Intelligence">
        <div style={{ padding: '24px', textAlign: 'center' }}>
          Analyzing PD Array levels and liquidity targets...
        </div>
      </Card>
    );
  }

  // Error state
  if (displayError) {
    return (
      <Card title="🎯 PD Array Intelligence">
        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>
          Error: {displayError}
          {onRefresh && (
            <button
              onClick={onRefresh}
              style={{
                marginLeft: '16px',
                padding: '8px 16px',
                background: '#f0f0f0',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Retry
            </button>
          )}
        </div>
      </Card>
    );
  }

  // Empty state
  if (!pdArrayIntelligence || pdArrayIntelligence.activePDArrays.length === 0) {
    return (
      <Card title="🎯 PD Array Intelligence">
        <EmptyState>
          <EmptyIcon>📊</EmptyIcon>
          <EmptyTitle>No PD Array Data Available</EmptyTitle>
          <EmptyMessage>
            Import your trading data to begin tracking FVGs, NWOG/NDOG levels, RD formations, and
            liquidity targets. The system will analyze your historical performance with each PD
            Array type.
          </EmptyMessage>
        </EmptyState>
      </Card>
    );
  }

  return (
    <Card
      title="🎯 PD Array Intelligence"
      actions={
        onRefresh ? (
          <button
            onClick={onRefresh}
            style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }}
          >
            🔄 Refresh
          </button>
        ) : undefined
      }
    >
      <Container className={className}>
        {/* Active PD Arrays */}
        <div>
          <SectionTitle>
            🔥 Active PD Arrays
            <PriorityBadge priority="HIGH">PRIORITY TARGETS</PriorityBadge>
          </SectionTitle>

          {pdArrayIntelligence.activePDArrays.map((array, index) => (
            <PDArrayCard key={index} arrayType={array.type} isActive={array.isActive}>
              <ArrayHeader>
                <ArrayType arrayType={array.type}>{array.type}</ArrayType>
                <ArrayStatus isActive={array.isActive}>
                  {array.isActive ? 'ACTIVE' : 'INACTIVE'}
                </ArrayStatus>
              </ArrayHeader>

              <LevelInfo>
                <LevelDetail>
                  <LevelValue>{array.level}</LevelValue>
                  <LevelLabel>Level</LevelLabel>
                </LevelDetail>
                <LevelDetail>
                  <LevelValue>{array.timeframe}</LevelValue>
                  <LevelLabel>Timeframe</LevelLabel>
                </LevelDetail>
                <LevelDetail>
                  <LevelValue>{array.age}</LevelValue>
                  <LevelLabel>Age</LevelLabel>
                </LevelDetail>
              </LevelInfo>

              <PerformanceStats>
                <StatsGrid>
                  <StatItem>
                    <StatValue>{array.performance.totalTrades}</StatValue>
                    <StatLabel>Trades</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatValue>{array.performance.winRate.toFixed(0)}%</StatValue>
                    <StatLabel>Win Rate</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatValue>{array.performance.avgRMultiple.toFixed(1)}R</StatValue>
                    <StatLabel>Avg R</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatValue>{array.performance.successRate.toFixed(0)}%</StatValue>
                    <StatLabel>Success Rate</StatLabel>
                  </StatItem>
                </StatsGrid>
              </PerformanceStats>

              <RecommendationText>
                <strong>Strategy:</strong> {array.recommendation}
              </RecommendationText>
            </PDArrayCard>
          ))}
        </div>

        {/* Summary Statistics */}
        <div>
          <SectionTitle>📊 PD Array Performance Summary</SectionTitle>
          <PDArrayCard arrayType="summary" isActive={true}>
            <StatsGrid>
              <StatItem>
                <StatValue>{pdArrayIntelligence.summary.totalActiveLevels}</StatValue>
                <StatLabel>Active Levels</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{pdArrayIntelligence.summary.bestPerformingType}</StatValue>
                <StatLabel>Best Type</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{pdArrayIntelligence.summary.overallSuccessRate.toFixed(0)}%</StatValue>
                <StatLabel>Overall Success</StatLabel>
              </StatItem>
              <StatItem>
                <StatValue>{pdArrayIntelligence.summary.avgRMultiple.toFixed(1)}R</StatValue>
                <StatLabel>Avg R-Multiple</StatLabel>
              </StatItem>
            </StatsGrid>

            <RecommendationText>
              <strong>Key Insights:</strong> {pdArrayIntelligence.summary.keyInsights.join(' • ')}
            </RecommendationText>
          </PDArrayCard>
        </div>
      </Container>
    </Card>
  );
};
