/*
 * ADHD Trading Dashboard - F1 Theme Styles
 * F1-inspired theme implementation
 */

@import './variables.css';

/* Base Styles */
body {
  font-family: var(--font-family-primary);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-family-secondary);
  font-weight: var(--font-weight-bold);
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  line-height: 1.2;
}

h1 {
  font-size: var(--font-size-4xl);
}

h2 {
  font-size: var(--font-size-3xl);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-md);
}

p {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--mercedes-green);
  text-decoration: none;
  transition: color var(--transition-fast) var(--transition-ease);
}

a:hover {
  color: var(--mercedes-green-light);
  text-decoration: underline;
}

/* Buttons */
.f1-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: var(--mercedes-green);
  color: var(--mercedes-white);
  border: none;
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-family-secondary);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: background-color var(--transition-fast) var(--transition-ease);
}

.f1-button:hover {
  background-color: var(--mercedes-green-light);
}

.f1-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 210, 190, 0.3);
}

.f1-button:disabled {
  background-color: var(--text-tertiary);
  cursor: not-allowed;
}

.f1-button--secondary {
  background-color: transparent;
  color: var(--mercedes-green);
  border: 2px solid var(--mercedes-green);
}

.f1-button--secondary:hover {
  background-color: rgba(0, 210, 190, 0.1);
}

/* Cards */
.f1-card {
  background-color: var(--bg-card);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.f1-card__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.f1-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  margin: 0;
}

/* Form Elements */
.f1-input {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-medium);
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-md);
  width: 100%;
  transition: border-color var(--transition-fast) var(--transition-ease);
}

.f1-input:focus {
  outline: none;
  border-color: var(--f1-red);
  box-shadow: 0 0 0 3px rgba(225, 6, 0, 0.1);
}

.f1-input::placeholder {
  color: var(--text-tertiary);
}

/* Tables */
.f1-table {
  width: 100%;
  border-collapse: collapse;
}

.f1-table th,
.f1-table td {
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border-light);
}

.f1-table th {
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  text-transform: uppercase;
  font-size: var(--font-size-xs);
  letter-spacing: 0.05em;
}

.f1-table tr:hover {
  background-color: var(--bg-hover);
}

/* Badges */
.f1-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25em 0.75em;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.f1-badge--success {
  background-color: rgba(0, 210, 190, 0.15);
  color: var(--mercedes-green);
}

.f1-badge--warning {
  background-color: rgba(255, 211, 32, 0.15);
  color: var(--f1-yellow);
}

.f1-badge--danger {
  background-color: rgba(192, 192, 192, 0.15);
  color: var(--mercedes-silver);
}

/* Utilities */
.f1-text-success {
  color: var(--text-success);
}
.f1-text-warning {
  color: var(--text-warning);
}
.f1-text-danger {
  color: var(--text-danger);
}

.f1-bg-success {
  background-color: var(--mercedes-green);
}
.f1-bg-warning {
  background-color: var(--f1-yellow);
}
.f1-bg-danger {
  background-color: var(--mercedes-silver);
}

/* Responsive utilities */
@media (max-width: 768px) {
  h1 {
    font-size: var(--font-size-3xl);
  }
  h2 {
    font-size: var(--font-size-2xl);
  }
  h3 {
    font-size: var(--font-size-xl);
  }

  .f1-card {
    padding: var(--spacing-md);
  }
}
