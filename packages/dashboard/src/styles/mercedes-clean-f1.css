/**
 * MERCEDES GREEN THEME - CLEAN F1 PIT WALL STYLE
 *
 * Fixes muddy teal layering issues by implementing clean, professional
 * F1 timing screen aesthetic with high contrast and minimal shadows.
 */

/* =============================================================================
   MAIN SESSION CARDS - Clean Professional Style
   ============================================================================= */

[data-theme='mercedes-green'] .session-card,
[data-theme='mercedes-green'] .enhanced-session-intelligence,
[data-theme='mercedes-green'] .live-session-intelligence,
[data-theme='mercedes-green'] .optimal-window-card,
[data-theme='mercedes-green'] .session-recommendations {
  /* SINGLE CLEAN BACKGROUND */
  background: var(--session-card-bg) !important;

  /* REMOVE ALL LAYERING EFFECTS */
  background-image: none !important;
  backdrop-filter: none !important;
  filter: none !important;

  /* SINGLE MERCEDES ACCENT STRIPE */
  border-left: 4px solid var(--session-card-accent) !important;
  border: 1px solid var(--session-card-border) !important;

  /* MINIMAL PROFESSIONAL SHADOW */
  box-shadow: 0 1px 3px var(--session-card-shadow) !important;

  /* HIGH CONTRAST TEXT */
  color: var(--session-text-primary) !important;
}

/* ACTIVE SESSION CARDS */
[data-theme='mercedes-green'] .session-card.active,
[data-theme='mercedes-green'] .optimal-window-active,
[data-theme='mercedes-green'] .session-card[data-active='true'] {
  background: var(--session-card-bg) !important;
  border-left: 4px solid var(--session-card-active-accent) !important;
  border: 1px solid rgba(0, 255, 229, 0.3) !important;
  box-shadow: 0 2px 6px var(--session-card-active-shadow) !important;
}

/* =============================================================================
   STYLED COMPONENTS OVERRIDES
   ============================================================================= */

/* Current Session Card */
[data-theme='mercedes-green'] .current-session-card {
  background: var(--session-card-bg) !important;
  border: 1px solid var(--session-card-border) !important;
  border-left: 4px solid var(--session-card-accent) !important;
  box-shadow: 0 1px 3px var(--session-card-shadow) !important;
}

[data-theme='mercedes-green'] .current-session-card::before {
  background: linear-gradient(
    90deg,
    var(--session-card-accent),
    var(--session-card-active-accent),
    var(--session-card-accent)
  ) !important;
}

/* ICT Session Cards */
[data-theme='mercedes-green'] .ict-session-card {
  background: var(--session-card-bg) !important;
  border: 1px solid var(--session-card-border) !important;
  border-left: 4px solid var(--session-card-accent) !important;
  box-shadow: 0 1px 3px var(--session-card-shadow) !important;
}

[data-theme='mercedes-green'] .ict-session-card.active {
  border-left: 4px solid var(--session-card-active-accent) !important;
  border: 1px solid rgba(0, 255, 229, 0.3) !important;
  box-shadow: 0 2px 6px var(--session-card-active-shadow) !important;
}

/* Window Cards */
[data-theme='mercedes-green'] .window-card {
  background: var(--session-card-bg) !important;
  border: 1px solid var(--session-card-border) !important;
  box-shadow: 0 1px 3px var(--session-card-shadow) !important;
}

[data-theme='mercedes-green'] .window-card.optimal {
  border-left: 3px solid var(--session-card-accent) !important;
  border: 1px solid rgba(0, 210, 190, 0.3) !important;
}

/* =============================================================================
   HIGH CONTRAST TEXT
   ============================================================================= */

[data-theme='mercedes-green'] .primary-text,
[data-theme='mercedes-green'] .session-title,
[data-theme='mercedes-green'] .metric-value,
[data-theme='mercedes-green'] h1,
[data-theme='mercedes-green'] h2,
[data-theme='mercedes-green'] h3,
[data-theme='mercedes-green'] h4 {
  color: var(--session-text-primary) !important;
  text-shadow: none !important;
}

[data-theme='mercedes-green'] .secondary-text,
[data-theme='mercedes-green'] .metric-label,
[data-theme='mercedes-green'] .session-description {
  color: var(--session-text-secondary) !important;
  text-shadow: none !important;
}

[data-theme='mercedes-green'] .accent-text,
[data-theme='mercedes-green'] .mercedes-accent,
[data-theme='mercedes-green'] .session-accent {
  color: var(--session-text-accent) !important;
  text-shadow: none !important;
}

/* =============================================================================
   REMOVE MUDDY TEAL LAYERING
   ============================================================================= */

/* Remove all gradient backgrounds that cause muddy layering */
[data-theme='mercedes-green'] *[style*='linear-gradient'] {
  background-image: none !important;
}

/* Remove excessive backdrop filters */
[data-theme='mercedes-green'] *[style*='backdrop-filter'] {
  backdrop-filter: none !important;
}

/* Remove teal shadows that muddy the interface */
[data-theme='mercedes-green'] *[style*='rgba(0, 210, 190'] {
  box-shadow: 0 1px 3px var(--session-card-shadow) !important;
}

/* =============================================================================
   SPECIFIC COMPONENT TARGETING
   ============================================================================= */

/* Session Focus Component Cards */
[data-theme='mercedes-green'] [class*='SessionCard'],
[data-theme='mercedes-green'] [class*='CurrentSession'],
[data-theme='mercedes-green'] [class*='ICTSession'],
[data-theme='mercedes-green'] [class*='WindowCard'] {
  background: var(--session-card-bg) !important;
  border: 1px solid var(--session-card-border) !important;
  border-left: 4px solid var(--session-card-accent) !important;
  box-shadow: 0 1px 3px var(--session-card-shadow) !important;
  background-image: none !important;
  backdrop-filter: none !important;
}

/* Active state for styled components */
[data-theme='mercedes-green'] [class*='SessionCard'][class*='active'],
[data-theme='mercedes-green'] [class*='ICTSession'][class*='active'],
[data-theme='mercedes-green'] [class*='WindowCard'][class*='optimal'] {
  border-left: 4px solid var(--session-card-active-accent) !important;
  border: 1px solid rgba(0, 255, 229, 0.3) !important;
  box-shadow: 0 2px 6px var(--session-card-active-shadow) !important;
}

/* =============================================================================
   PERFORMANCE METRICS STYLING
   ============================================================================= */

[data-theme='mercedes-green'] .win-rate,
[data-theme='mercedes-green'] .session-metric,
[data-theme='mercedes-green'] .performance-stat {
  color: var(--session-text-primary) !important;
  font-weight: 600 !important;
}

[data-theme='mercedes-green'] .metric-positive {
  color: var(--session-card-accent) !important;
}

[data-theme='mercedes-green'] .metric-excellent {
  color: var(--session-card-active-accent) !important;
}

/* =============================================================================
   PD ARRAY TAB - CLEAN F1 PIT WALL STYLE
   ============================================================================= */

/* PD Array Cards - Remove muddy gradient layering */
[data-theme='mercedes-green'] [class*='PDArrayCard'] {
  background: var(--session-card-bg) !important;
  border: 1px solid var(--session-card-border) !important;
  border-left: 4px solid var(--session-card-accent) !important;
  box-shadow: 0 1px 3px var(--session-card-shadow) !important;
  background-image: none !important;
  backdrop-filter: none !important;
}

/* Active PD Array Cards */
[data-theme='mercedes-green'] [class*='PDArrayCard'][data-active='true'],
[data-theme='mercedes-green'] [class*='PDArrayCard'].active {
  border-left: 4px solid var(--session-card-active-accent) !important;
  border: 1px solid rgba(0, 255, 229, 0.3) !important;
  box-shadow: 0 2px 6px var(--session-card-active-shadow) !important;
}

/* PD Array Type-Specific Accent Colors (Clean, No Gradients) */
[data-theme='mercedes-green'] [data-array-type='FVG'] {
  border-left: 4px solid var(--session-card-accent) !important;
}

[data-theme='mercedes-green'] [data-array-type='NWOG'] {
  border-left: 4px solid var(--session-card-accent) !important;
}

[data-theme='mercedes-green'] [data-array-type='NDOG'] {
  border-left: 4px solid var(--session-card-accent) !important;
}

[data-theme='mercedes-green'] [data-array-type='RD'] {
  border-left: 4px solid var(--session-card-accent) !important;
}

[data-theme='mercedes-green'] [data-array-type='Liquidity'] {
  border-left: 4px solid var(--session-card-accent) !important;
}

/* Performance Stats Cards */
[data-theme='mercedes-green'] [class*='PerformanceStats'] {
  background: var(--session-card-bg) !important;
  border: 1px solid var(--session-card-border) !important;
  box-shadow: 0 1px 3px var(--session-card-shadow) !important;
}

/* PD Array Text Elements */
[data-theme='mercedes-green'] [class*='ArrayType'],
[data-theme='mercedes-green'] [class*='LevelValue'],
[data-theme='mercedes-green'] [class*='StatValue'] {
  color: var(--session-text-primary) !important;
}

[data-theme='mercedes-green'] [class*='LevelLabel'],
[data-theme='mercedes-green'] [class*='StatLabel'],
[data-theme='mercedes-green'] [class*='RecommendationText'] {
  color: var(--session-text-secondary) !important;
}

/* Priority Badges - Use Mercedes Theme Colors */
[data-theme='mercedes-green'] [class*='PriorityBadge'][data-priority='HIGH'] {
  background: var(--session-card-active-accent) !important;
  color: var(--session-card-bg) !important;
}

[data-theme='mercedes-green'] [class*='PriorityBadge'][data-priority='MEDIUM'] {
  background: var(--session-card-accent) !important;
  color: var(--session-text-primary) !important;
}

[data-theme='mercedes-green'] [class*='PriorityBadge'][data-priority='LOW'] {
  background: var(--session-card-border) !important;
  color: var(--session-text-secondary) !important;
}

/* Array Status Badges */
[data-theme='mercedes-green'] [class*='ArrayStatus'][data-active='true'] {
  background: var(--session-card-accent) !important;
  color: var(--session-text-primary) !important;
}

[data-theme='mercedes-green'] [class*='ArrayStatus'][data-active='false'] {
  background: var(--session-card-border) !important;
  color: var(--session-text-secondary) !important;
}

/* Remove All PD Array Gradient Backgrounds */
[data-theme='mercedes-green'] [style*='linear-gradient'][class*='PDArray'] {
  background: var(--session-card-bg) !important;
  background-image: none !important;
}

/* Clean Empty State Styling */
[data-theme='mercedes-green'] [class*='EmptyState'] {
  background: var(--session-card-bg) !important;
  border: 1px solid var(--session-card-border) !important;
  border-radius: 12px !important;
}

[data-theme='mercedes-green'] [class*='EmptyTitle'] {
  color: var(--session-text-primary) !important;
}

[data-theme='mercedes-green'] [class*='EmptyMessage'] {
  color: var(--session-text-secondary) !important;
}
