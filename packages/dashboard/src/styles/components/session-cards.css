/**
 * SESSION CARD COMPONENT SYSTEM
 *
 * Clean, professional session card styling using CSS variables.
 * Eliminates hardcoded colors and provides consistent theming.
 */

/* =============================================================================
   BASE SESSION CARD STYLES
   ============================================================================= */

.session-card {
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-left: 4px solid var(--session-card-accent);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
  
  /* Professional shadow system */
  box-shadow: 
    var(--shadow-sm),
    0 0 0 1px var(--session-card-border);
}

/* =============================================================================
   SESSION CARD STATES
   ============================================================================= */

.session-card.active {
  border-left-color: var(--session-active);
  box-shadow: 
    var(--shadow-md),
    0 0 0 1px var(--session-active);
}

.session-card.optimal {
  border-left-color: var(--session-optimal);
  box-shadow: 
    var(--shadow-md),
    0 0 0 1px var(--session-optimal);
}

.session-card.active.optimal {
  border-left-color: var(--session-optimal);
  box-shadow: 
    var(--shadow-md),
    0 0 0 1px var(--session-optimal),
    var(--shadow-accent);
}

/* =============================================================================
   SESSION CARD VARIANTS
   ============================================================================= */

.current-session-card {
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-accent);
  border-left: 4px solid var(--session-card-accent);
  border-radius: 12px;
  padding: 20px;
  position: relative;
  overflow: hidden;
  box-shadow: 
    var(--shadow-md),
    0 0 0 1px var(--session-card-accent);
}

.current-session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--session-card-accent),
    var(--session-card-active-accent),
    var(--session-card-accent)
  );
  animation: pulse 2s ease-in-out infinite;
}

.ict-session-card {
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-left: 4px solid var(--session-card-accent);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.ict-session-card.active {
  border-left-color: var(--session-active);
  border-color: var(--session-active);
  box-shadow: 
    var(--shadow-md),
    0 0 0 1px var(--session-active);
}

.ict-session-card.optimal {
  border-left-color: var(--session-optimal);
  border-color: var(--session-optimal);
  box-shadow: 
    var(--shadow-md),
    0 0 0 1px var(--session-optimal);
}

/* =============================================================================
   WINDOW CARDS
   ============================================================================= */

.window-card {
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.window-card.optimal {
  border-left: 3px solid var(--session-optimal);
  border-color: var(--session-optimal);
  box-shadow: 
    var(--shadow-sm),
    0 0 0 1px var(--session-optimal);
}

/* =============================================================================
   SESSION CARD TEXT ELEMENTS
   ============================================================================= */

.session-card .primary-text,
.session-card .session-title,
.session-card .metric-value {
  color: var(--session-text-primary);
  text-shadow: none;
}

.session-card .secondary-text,
.session-card .metric-label,
.session-card .session-description {
  color: var(--session-text-secondary);
  text-shadow: none;
}

.session-card .accent-text,
.session-card .mercedes-accent,
.session-card .session-accent {
  color: var(--session-text-accent);
  text-shadow: none;
}

/* =============================================================================
   LIVE INDICATORS
   ============================================================================= */

.live-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(
    135deg,
    var(--session-active),
    var(--session-optimal)
  );
  color: var(--session-text-primary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-accent);
}

.live-indicator::before {
  content: '';
  width: 6px;
  height: 6px;
  background: var(--session-text-primary);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* =============================================================================
   ANIMATIONS
   ============================================================================= */

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* =============================================================================
   RESPONSIVE DESIGN
   ============================================================================= */

@media (max-width: 768px) {
  .session-card {
    padding: 12px;
    margin-bottom: 12px;
  }
  
  .current-session-card {
    padding: 16px;
  }
  
  .window-card {
    padding: 6px;
  }
}
