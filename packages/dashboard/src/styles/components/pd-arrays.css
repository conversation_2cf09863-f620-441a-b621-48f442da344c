/**
 * PD ARRAY COMPONENT SYSTEM
 *
 * Scalable PD Array styling using CSS variables and data attributes.
 * Eliminates hardcoded switch statements and provides consistent theming.
 */

/* =============================================================================
   BASE PD ARRAY CARD STYLES
   ============================================================================= */

.pd-array-card {
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-left: 4px solid var(--session-card-accent);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

/* =============================================================================
   PD ARRAY ACTIVE STATES
   ============================================================================= */

.pd-array-card[data-active='true'] {
  border-left-color: var(--pd-array-accent, var(--session-card-accent));
  box-shadow: 
    var(--shadow-sm),
    0 0 0 1px var(--pd-array-accent, var(--session-card-accent));
}

/* =============================================================================
   ARRAY TYPE ACCENT SYSTEM
   ============================================================================= */

/* FVG Arrays - Information Blue */
.pd-array-card[data-array-type='fvg'][data-active='true'] {
  --pd-array-accent: var(--info-color);
}

/* NWOG Arrays - Success Green */
.pd-array-card[data-array-type='nwog'][data-active='true'] {
  --pd-array-accent: var(--success-color);
}

/* NDOG Arrays - Warning Orange */
.pd-array-card[data-array-type='ndog'][data-active='true'] {
  --pd-array-accent: var(--warning-color);
}

/* RD Arrays - Error Red */
.pd-array-card[data-array-type='rd'][data-active='true'] {
  --pd-array-accent: var(--error-color);
}

/* Liquidity Arrays - Secondary Purple */
.pd-array-card[data-array-type='liquidity'][data-active='true'] {
  --pd-array-accent: var(--secondary-color);
}

/* Summary Cards - Primary Theme Color */
.pd-array-card[data-array-type='summary'][data-active='true'] {
  --pd-array-accent: var(--primary-color);
}

/* =============================================================================
   ARRAY TYPE TEXT COLORS
   ============================================================================= */

.array-type[data-array-type='fvg'] {
  color: var(--info-color);
}

.array-type[data-array-type='nwog'] {
  color: var(--success-color);
}

.array-type[data-array-type='ndog'] {
  color: var(--warning-color);
}

.array-type[data-array-type='rd'] {
  color: var(--error-color);
}

.array-type[data-array-type='liquidity'] {
  color: var(--secondary-color);
}

.array-type[data-array-type='summary'] {
  color: var(--primary-color);
}

/* =============================================================================
   ARRAY STATUS BADGES
   ============================================================================= */

.array-status[data-active='true'] {
  background: var(--success-color);
  color: var(--session-text-primary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.array-status[data-active='false'] {
  background: var(--session-card-border);
  color: var(--session-text-secondary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

/* =============================================================================
   PRIORITY BADGES
   ============================================================================= */

.priority-badge[data-priority='HIGH'] {
  background: var(--error-color);
  color: var(--session-text-primary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge[data-priority='MEDIUM'] {
  background: var(--warning-color);
  color: var(--session-text-primary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge[data-priority='LOW'] {
  background: var(--session-card-border);
  color: var(--session-text-secondary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

/* =============================================================================
   PERFORMANCE STATS CARDS
   ============================================================================= */

.performance-stats {
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
  box-shadow: var(--shadow-sm);
}

/* =============================================================================
   PD ARRAY TEXT ELEMENTS
   ============================================================================= */

.pd-array-card .level-value,
.pd-array-card .stat-value {
  color: var(--session-text-primary);
  font-weight: 600;
}

.pd-array-card .level-label,
.pd-array-card .stat-label,
.pd-array-card .recommendation-text {
  color: var(--session-text-secondary);
}

.pd-array-card .array-type {
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* =============================================================================
   EMPTY STATE STYLING
   ============================================================================= */

.empty-state {
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-radius: 12px;
  text-align: center;
  padding: 40px 20px;
  color: var(--session-text-secondary);
}

.empty-title {
  color: var(--session-text-primary);
  font-size: 18px;
  margin-bottom: 8px;
}

.empty-message {
  color: var(--session-text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

/* =============================================================================
   RESPONSIVE DESIGN
   ============================================================================= */

@media (max-width: 768px) {
  .pd-array-card {
    padding: 12px;
    margin-bottom: 8px;
  }
  
  .performance-stats {
    padding: 8px;
  }
  
  .empty-state {
    padding: 24px 16px;
  }
}
