/**
 * Theme Test Panel
 *
 * Component for testing theme switching and visual consistency.
 * Displays sample components in different themes to verify styling.
 */

import React from 'react';
import styled from 'styled-components';
import { useTheme } from '@adhd-trading-dashboard/shared';

const TestPanel = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-radius: 12px;
  padding: 16px;
  min-width: 300px;
  z-index: 1000;
  box-shadow: var(--shadow-lg);
`;

const ThemeButton = styled.button<{ isActive: boolean }>`
  background: ${({ isActive }) => 
    isActive ? 'var(--primary-color)' : 'var(--session-card-bg)'};
  color: ${({ isActive }) => 
    isActive ? 'var(--session-text-primary)' : 'var(--session-text-secondary)'};
  border: 1px solid var(--session-card-border);
  border-radius: 6px;
  padding: 8px 12px;
  margin: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.2s ease;

  &:hover {
    background: var(--primary-color);
    color: var(--session-text-primary);
  }
`;

const SampleCard = styled.div`
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-left: 4px solid var(--session-card-accent);
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  box-shadow: var(--shadow-sm);
`;

const SampleText = styled.div`
  color: var(--session-text-primary);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
`;

const SampleSecondary = styled.div`
  color: var(--session-text-secondary);
  font-size: 12px;
`;

const StatusBadge = styled.div<{ status: 'success' | 'warning' | 'error' | 'info' }>`
  display: inline-block;
  background: ${({ status }) => {
    switch (status) {
      case 'success': return 'var(--success-color)';
      case 'warning': return 'var(--warning-color)';
      case 'error': return 'var(--error-color)';
      case 'info': return 'var(--info-color)';
      default: return 'var(--session-card-border)';
    }
  }};
  color: var(--session-text-primary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin: 2px;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: var(--session-text-secondary);
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  
  &:hover {
    color: var(--session-text-primary);
  }
`;

interface ThemeTestPanelProps {
  onClose: () => void;
}

export const ThemeTestPanel: React.FC<ThemeTestPanelProps> = ({ onClose }) => {
  const { theme, setTheme } = useTheme();

  const themes = [
    { id: 'mercedes-green', name: 'Mercedes Green' },
    { id: 'f1-official', name: 'F1 Official' },
    { id: 'dark', name: 'Dark' },
  ];

  return (
    <TestPanel>
      <CloseButton onClick={onClose}>×</CloseButton>
      
      <h3 style={{ color: 'var(--session-text-primary)', margin: '0 0 12px 0' }}>
        Theme Test Panel
      </h3>
      
      <div style={{ marginBottom: '16px' }}>
        <div style={{ color: 'var(--session-text-secondary)', fontSize: '12px', marginBottom: '8px' }}>
          Current: {theme.name}
        </div>
        {themes.map((t) => (
          <ThemeButton
            key={t.id}
            isActive={theme.name === t.id}
            onClick={() => setTheme(t.id)}
          >
            {t.name}
          </ThemeButton>
        ))}
      </div>

      <SampleCard>
        <SampleText>Sample Session Card</SampleText>
        <SampleSecondary>This shows the theme colors in action</SampleSecondary>
        <div style={{ marginTop: '8px' }}>
          <StatusBadge status="success">SUCCESS</StatusBadge>
          <StatusBadge status="warning">WARNING</StatusBadge>
          <StatusBadge status="error">ERROR</StatusBadge>
          <StatusBadge status="info">INFO</StatusBadge>
        </div>
      </SampleCard>

      <div style={{ 
        color: 'var(--session-text-secondary)', 
        fontSize: '10px', 
        marginTop: '12px',
        lineHeight: '1.4'
      }}>
        Test theme switching to verify:
        <br />• Clean backgrounds (no muddy layering)
        <br />• Proper accent colors
        <br />• High contrast text
        <br />• Consistent borders
      </div>
    </TestPanel>
  );
};

export default ThemeTestPanel;
