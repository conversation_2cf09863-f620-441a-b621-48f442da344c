// React import handled by JSX transform
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@adhd-trading-dashboard/shared';
import { AppRoutes } from './routes';
import AppErrorBoundary from './components/AppErrorBoundary';
import './styles/theme-variables.css';
import './styles/components/session-cards.css';
import './styles/components/pd-arrays.css';

/**
 * Main App component for the ADHD Trading Dashboard
 * Using BrowserRouter for better URL structure
 */
function App() {
  return (
    <AppErrorBoundary>
      <ThemeProvider initialTheme="mercedes-green">
        <BrowserRouter>
          <AppRoutes />
        </BrowserRouter>
      </ThemeProvider>
    </AppErrorBoundary>
  );
}

export default App;
