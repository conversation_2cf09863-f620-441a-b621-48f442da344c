/**
 * COMPREHENSIVE TIME DATA FLOW ANALYSIS
 *
 * This script traces the complete time flow through the Daily Guide system
 * to identify why current time displays correctly while NY trading sessions show incorrect timing.
 */
declare function getUserTimezone(): string;
declare function getTimezoneAbbreviation(timezone: any, date?: Date): any;
declare function convertNYToLocal(nyTime: any, userTimezone: any): string;
declare function getCurrentDualTime(userTimezone: any): {
    nyTime: string;
    localTime: string;
    nyTimezone: any;
    localTimezone: any;
    formatted: string;
};
declare function getCurrentNYMinutes(): number;
declare function timeToMinutes(timeStr: any): any;
declare function formatTimeInterval(totalMinutes: any): {
    totalMinutes: any;
    hours: number;
    minutes: number;
    formatted: string;
};
declare function convertSessionToDualTime(nyStart: any, nyEnd: any, userTimezone: any): {
    nyStart: any;
    nyEnd: any;
    localStart: string;
    localEnd: string;
    formatted: string;
};
declare const now: Date;
declare const userTimezone: string;
declare namespace dualTime {
    export { nyTime };
    export { localTime };
    export { nyTimezone };
    export { localTimezone };
    export let formatted: string;
}
declare const currentNYMinutes: number;
declare const sessions: {
    name: string;
    start: string;
    end: string;
}[];
declare const nyTime: string;
declare const localTime: string;
declare const nyTimezone: any;
declare const localTimezone: any;
//# sourceMappingURL=time-flow-analysis.d.ts.map