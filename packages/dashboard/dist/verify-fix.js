/**
 * Verify the timezone conversion fix is working
 */
// Simulate the FIXED convertNYToLocal function
const getUserTimezone = () => {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
};
const convertNYToLocal = (nyTime, userTimezone) => {
    const timezone = userTimezone || getUserTimezone();
    // Parse the NY time
    const [hours, minutes] = nyTime.split(':').map(Number);
    // FIXED: Use the simplest and most reliable approach
    // Create a date representing the current day
    const today = new Date();
    // Create a date string in ISO format for today at the specified time
    // We'll treat this as if it's in NY timezone
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const timeStr = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:00`;
    // Create the datetime string
    const isoString = `${year}-${month}-${day}T${timeStr}`;
    // Parse this as a local date first
    const localDate = new Date(isoString);
    // Now we need to adjust for the fact that we want this to represent NY time
    // Get current offset between local time and NY time
    const nowLocal = new Date();
    const nowInNY = new Date(nowLocal.toLocaleString('en-US', { timeZone: 'America/New_York' }));
    const nowInLocal = new Date(nowLocal.toLocaleString('en-US', { timeZone: timezone }));
    // Calculate the offset in milliseconds
    const offsetMs = nowInLocal.getTime() - nowInNY.getTime();
    // Apply this offset to our target time
    const adjustedDate = new Date(localDate.getTime() + offsetMs);
    // Format in user's timezone
    return adjustedDate.toLocaleTimeString('en-GB', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
};
console.log('🔧 VERIFYING TIMEZONE CONVERSION FIX');
console.log('====================================');
const now = new Date();
const userTimezone = getUserTimezone();
console.log('Current Context:');
console.log('User Timezone:', userTimezone);
console.log('Current NY Time:', now.toLocaleTimeString('en-US', { timeZone: 'America/New_York', hour12: false }));
console.log('Current Local Time:', now.toLocaleTimeString('en-GB', { timeZone: userTimezone, hour12: false }));
console.log('\n✅ TESTING FIXED CONVERSION:');
const testResults = [
    { ny: '09:30', expected: '14:30' },
    { ny: '11:50', expected: '16:50' },
    { ny: '15:15', expected: '20:15' }
];
testResults.forEach(test => {
    const result = convertNYToLocal(test.ny, userTimezone);
    const isCorrect = result === test.expected;
    console.log(`${test.ny} NY → ${result} ${isCorrect ? '✅' : '❌'} (Expected: ${test.expected})`);
});
console.log('\n🎯 SUMMARY:');
console.log('The timezone conversion fix should now show:');
console.log('- NY Open (09:30) as 14:30 Irish time');
console.log('- Lunch Macro (11:50) as 16:50 Irish time');
console.log('- MOC (15:15) as 20:15 Irish time');
console.log('- All times correctly 5 hours ahead of NY time (EDT to Irish Summer Time)');
//# sourceMappingURL=verify-fix.js.map