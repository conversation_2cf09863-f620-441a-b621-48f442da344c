/**
 * Trading Dashboard Types
 *
 * Type definitions for the Trading Dashboard feature
 */
import { PerformanceMetrics } from '@adhd-trading-dashboard/shared';
export type { PerformanceMetrics };
/**
 * Dashboard Trade Interface
 *
 * Extended trade interface for dashboard display with additional properties
 * that are commonly used in trading dashboard calculations and displays.
 */
export interface DashboardTrade {
    id: string | number;
    date: string;
    direction: 'Long' | 'Short';
    model?: string;
    session?: string;
    setup?: string;
    entry?: string;
    exit?: string;
    market?: string;
    rMultiple?: number;
    patternQuality?: number;
    win?: boolean;
    entryPrice?: number;
    exitPrice?: number;
    risk?: number;
    pnl?: number;
    dolTarget?: string;
    rdType?: string;
    entryVersion?: string;
    drawOnLiquidity?: string;
}
/**
 * PerformanceMetric
 *
 * Represents a single performance metric
 */
export interface PerformanceMetric {
    title: string;
    value: string | number;
    change?: number;
    isPositive?: boolean;
}
/**
 * ChartDataPoint
 *
 * Represents a single data point for performance charts
 */
export interface ChartDataPoint {
    date: string;
    pnl: number;
    cumulative: number;
}
/**
 * SetupPerformance
 *
 * Represents performance metrics for a specific setup
 */
export interface SetupPerformance {
    name: string;
    winRate: number;
    avgRMultiple: number;
    totalTrades: number;
    pnl: number;
}
/**
 * SessionPerformance
 *
 * Represents performance metrics for a specific trading session
 */
export interface SessionPerformance {
    name: string;
    winRate: number;
    avgRMultiple: number;
    totalTrades: number;
    pnl: number;
}
/**
 * DashboardState
 *
 * Represents the state of the trading dashboard
 */
export interface DashboardState {
    trades: DashboardTrade[];
    performanceMetrics: PerformanceMetric[];
    chartData: ChartDataPoint[];
    setupPerformance: SetupPerformance[];
    sessionPerformance: SessionPerformance[];
    isLoading: boolean;
    error: string | null;
}
//# sourceMappingURL=index.d.ts.map