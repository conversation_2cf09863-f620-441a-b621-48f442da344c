/**
 * Enhanced Setup Intelligence Hook
 *
 * Analyzes primary_setup, secondary_setup, and liquidity_taken combinations
 * from actual trade data to provide sophisticated setup recommendations.
 */
export interface SetupPerformance {
    setupName: string;
    totalTrades: number;
    winRate: number;
    avgRMultiple: number;
    avgQuality: number;
    bestSession: string;
    successRate: number;
}
export interface SetupCombination {
    primary: string;
    secondary: string;
    liquidity: string;
    performance: {
        totalTrades: number;
        winRate: number;
        avgRMultiple: number;
        avgQuality: number;
        totalPnL: number;
    };
    sessions: {
        sessionName: string;
        winRate: number;
        trades: number;
    }[];
    recommendation: string;
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
}
export interface LiquidityIntelligence {
    liquidityTarget: string;
    performance: {
        totalTrades: number;
        winRate: number;
        avgRMultiple: number;
        successRate: number;
    };
    bestModels: string[];
    bestSessions: string[];
    recommendation: string;
}
export interface SetupIntelligenceData {
    topPrimarySetups: SetupPerformance[];
    topSecondarySetups: SetupPerformance[];
    bestCombinations: SetupCombination[];
    liquidityIntelligence: LiquidityIntelligence[];
    currentRecommendations: {
        primarySetup: string;
        secondarySetup: string;
        liquidityTarget: string;
        reasoning: string;
        expectedWinRate: number;
        expectedRMultiple: number;
    };
}
/**
 * Enhanced Setup Intelligence Hook
 */
export declare const useEnhancedSetupIntelligence: () => {
    setupIntelligence: SetupIntelligenceData;
    isLoading: boolean;
    error: string | null;
    refresh: () => void;
};
//# sourceMappingURL=useEnhancedSetupIntelligence.d.ts.map