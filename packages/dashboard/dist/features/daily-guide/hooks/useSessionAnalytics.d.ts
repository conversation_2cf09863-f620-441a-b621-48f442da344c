/**
 * Session Analytics Hook
 *
 * Analyzes user's trading performance by session timing using real trade data.
 * Provides personalized insights based on actual trading patterns.
 */
export interface SessionPerformance {
    hour: number;
    label: string;
    winRate: number;
    totalTrades: number;
    avgRMultiple: number;
    totalPnL: number;
    bestSetup: string;
    performance: 'excellent' | 'good' | 'average' | 'poor' | 'avoid';
}
export interface CurrentSessionRecommendation {
    currentHour: number;
    sessionLabel: string;
    recommendation: 'high' | 'medium' | 'low' | 'avoid';
    winRate: number;
    bestSetups: string[];
    riskLevel: 'low' | 'medium' | 'high';
    actionItems: string[];
}
export interface SessionAnalytics {
    sessionPerformance: SessionPerformance[];
    currentRecommendation: CurrentSessionRecommendation;
    bestPerformingHours: number[];
    worstPerformingHours: number[];
    totalAnalyzedTrades: number;
    lastUpdated: Date;
}
/**
 * Session Analytics Hook
 */
export declare const useSessionAnalytics: () => {
    analytics: SessionAnalytics;
    isLoading: boolean;
    error: string | null;
    refresh: () => void;
};
//# sourceMappingURL=useSessionAnalytics.d.ts.map