/**
 * Pattern Quality Scoring System Hook
 *
 * Real-time quality assessment (1-5 scale) replacing subjective pattern ratings with:
 * - PD Array confluence scoring
 * - FVG characteristics assessment
 * - RD strength evaluation
 * - Confirmation signal integration
 */
import type { CompleteTradeData } from '@adhd-trading-dashboard/shared';
export interface PatternQualityScore {
    totalScore: number;
    maxScore: number;
    breakdown: {
        pdArrayConfluence: number;
        fvgCharacteristics: number;
        rdStrength: number;
        confirmationSignals: number;
        volumeConfirmation: number;
    };
    rating: 'POOR' | 'FAIR' | 'GOOD' | 'EXCELLENT' | 'EXCEPTIONAL';
    recommendation: string;
    expectedWinProbability: number;
}
export interface LivePatternAnalysis {
    currentScore: PatternQualityScore;
    historicalAccuracy: number;
    scoreDistribution: {
        score: number;
        count: number;
        winRate: number;
        avgRMultiple: number;
    }[];
}
/**
 * Pattern Quality Scoring Hook
 */
export declare const usePatternQualityScoring: () => {
    analysis: LivePatternAnalysis;
    isLoading: boolean;
    error: string | null;
    calculatePatternQuality: (trade: CompleteTradeData) => PatternQualityScore;
    refresh: () => void;
};
//# sourceMappingURL=usePatternQualityScoring.d.ts.map