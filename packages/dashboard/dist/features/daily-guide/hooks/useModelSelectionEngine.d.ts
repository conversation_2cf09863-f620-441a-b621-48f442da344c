/**
 * Model Selection Engine Hook
 *
 * Intelligent real-time model selection (RD-Cont vs FVG-RD) based on:
 * - Market volatility analysis
 * - Liquidity context assessment
 * - Timeframe confluence logic
 * - Previous session performance impact
 */
export type ModelType = 'RD-Cont' | 'FVG-RD';
export type VolatilityLevel = 'low' | 'medium' | 'high';
export type LiquidityContext = 'void' | 'reaction' | 'mixed';
export type ConfidenceLevel = 'LOW' | 'MEDIUM' | 'HIGH';
export interface MarketConditions {
    volatility: VolatilityLevel;
    liquidityContext: LiquidityContext;
    htfTrend: 'bullish' | 'bearish' | 'neutral';
    previousSessionSuccess: ModelType | null;
}
export interface ModelRecommendation {
    recommendedModel: ModelType;
    probability: number;
    confidence: ConfidenceLevel;
    reasoning: string;
    alternativeModel: ModelType;
    alternativeCondition: string;
    marketConditions: MarketConditions;
}
export interface ModelPerformanceStats {
    model: ModelType;
    totalTrades: number;
    winRate: number;
    avgRMultiple: number;
    recentPerformance: number;
    volatilityPerformance: {
        low: {
            winRate: number;
            avgR: number;
            trades: number;
        };
        medium: {
            winRate: number;
            avgR: number;
            trades: number;
        };
        high: {
            winRate: number;
            avgR: number;
            trades: number;
        };
    };
}
/**
 * Model Selection Engine Hook
 */
export declare const useModelSelectionEngine: () => {
    recommendation: ModelRecommendation;
    modelStats: Record<ModelType, ModelPerformanceStats>;
    isLoading: boolean;
    error: string | null;
    refresh: () => void;
};
//# sourceMappingURL=useModelSelectionEngine.d.ts.map