import { TradingPlanItem } from '../types';
/**
 * Use Daily Guide Hook
 *
 * A custom hook for the daily guide feature.
 *
 * @returns The daily guide state and actions
 */
export declare function useDailyGuide(): {
    selectedDate: string;
    marketOverview: import("../types").MarketOverview | null;
    tradingPlan: import("../types").TradingPlan | null;
    keyPriceLevels: import("../types").KeyPriceLevel[];
    watchlist: import("../types").WatchlistItem[];
    marketNews: import("../types").MarketNewsItem[];
    isLoading: boolean;
    error: string | null;
    tradingPlanItems: TradingPlanItem[];
    tradingPlanCompletion: number;
    marketSentiment: import("../types").MarketSentiment;
    marketSummary: string;
    lastUpdated: string | null;
    currentDate: string;
    onDateChange: (date: string) => void;
    onTradingPlanItemToggle: (id: string, completed: boolean) => void;
    onAddTradingPlanItem: (item: TradingPlanItem) => void;
    onRemoveTradingPlanItem: (id: string) => void;
    onRefresh: () => void;
};
//# sourceMappingURL=useDailyGuide.d.ts.map