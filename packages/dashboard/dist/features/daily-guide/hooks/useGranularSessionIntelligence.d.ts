/**
 * Granular Session Intelligence Hook
 *
 * Detailed session breakdown with optimal entry timing:
 * - 15-30 minute performance windows
 * - Real-time session alerts and guidance
 * - Session transition intelligence
 * - Optimal window recommendations
 */
import { ModelType } from './useModelSelectionEngine';
export interface TimeWindow {
    start: string;
    end: string;
    label: string;
    description: string;
}
export interface SessionWindow {
    window: TimeWindow;
    performance: {
        totalTrades: number;
        winningTrades: number;
        winRate: number;
        avgRMultiple: number;
        totalPnL: number;
    };
    modelPreference: ModelType | null;
    isOptimal: boolean;
    isPrimary: boolean;
    recommendation: string;
}
export interface SessionAnalysis {
    sessionName: string;
    sessionType: 'NY_OPEN' | 'LUNCH_MACRO' | 'MOC' | 'PRE_MARKET';
    windows: SessionWindow[];
    overallPerformance: {
        totalTrades: number;
        winRate: number;
        avgRMultiple: number;
        bestWindow: TimeWindow | null;
        worstWindow: TimeWindow | null;
    };
    currentStatus: {
        isActive: boolean;
        currentWindow: TimeWindow | null;
        timeRemaining: number;
        nextWindow: TimeWindow | null;
        recommendation: string;
    };
}
export interface LiveSessionGuidance {
    currentTime: string;
    activeSession: SessionAnalysis | null;
    nextSession: SessionAnalysis | null;
    timeToNextOptimal: number;
    currentRecommendation: string;
    urgencyLevel: 'LOW' | 'MEDIUM' | 'HIGH';
}
/**
 * Granular Session Intelligence Hook
 */
export declare const useGranularSessionIntelligence: () => {
    sessionAnalyses: SessionAnalysis[];
    liveGuidance: LiveSessionGuidance;
    isLoading: boolean;
    error: string | null;
    refresh: () => void;
};
//# sourceMappingURL=useGranularSessionIntelligence.d.ts.map