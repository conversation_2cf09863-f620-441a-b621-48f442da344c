/**
 * PD Array Analytics Hook
 *
 * Analyzes user's actual trading performance with ICT PD Array elements and combinations.
 * Provides dynamic, performance-driven recommendations for the Daily Trading Plan.
 */
export type PDArrayElement = 'FVG' | 'NWOG' | 'NDOG' | 'Liquidity';
export type PDArrayCombination = 'FVG+NWOG' | 'FVG+Liquidity' | 'NWOG+Liquidity' | 'FVG+NWOG+Liquidity' | 'Single';
export interface ElementPerformance {
    element: PDArrayElement;
    totalTrades: number;
    winningTrades: number;
    winRate: number;
    avgRMultiple: number;
    totalPnL: number;
    bestSession: string;
    trend: 'improving' | 'declining' | 'stable';
}
export interface CombinationPerformance {
    combination: PDArrayCombination;
    totalTrades: number;
    winningTrades: number;
    winRate: number;
    avgRMultiple: number;
    totalPnL: number;
    effectiveness: 'excellent' | 'good' | 'average' | 'poor' | 'avoid';
}
export interface WeeklyComparison {
    currentWeek: {
        startDate: string;
        endDate: string;
        totalTrades: number;
        winRate: number;
        bestElements: PDArrayElement[];
        bestCombinations: PDArrayCombination[];
    };
    previousWeek: {
        startDate: string;
        endDate: string;
        totalTrades: number;
        winRate: number;
        bestElements: PDArrayElement[];
        bestCombinations: PDArrayCombination[];
    } | null;
    improvement: {
        winRateChange: number;
        volumeChange: number;
        elementShifts: string[];
        recommendations: string[];
    };
}
export interface DynamicActionItem {
    id: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
    category: 'element-focus' | 'combination-strategy' | 'risk-management' | 'psychology';
    basedOn: 'current-performance' | 'weekly-comparison' | 'trend-analysis';
    confidence: number;
}
export interface PDArrayAnalytics {
    elementPerformance: ElementPerformance[];
    combinationPerformance: CombinationPerformance[];
    weeklyComparison: WeeklyComparison;
    dynamicActionItems: DynamicActionItem[];
    riskGuidelines: {
        hotStreakElements: PDArrayElement[];
        coldStreakElements: PDArrayElement[];
        recommendedPositionSizing: string;
        riskAdjustments: string[];
    };
    performanceNotes: {
        weeklyPatterns: string[];
        timeBasedInsights: string[];
        psychologyInsights: string[];
    };
    totalAnalyzedTrades: number;
    lastUpdated: Date;
}
/**
 * PD Array Analytics Hook
 */
export declare const usePDArrayAnalytics: () => {
    analytics: PDArrayAnalytics;
    isLoading: boolean;
    error: string | null;
    refresh: () => void;
};
//# sourceMappingURL=usePDArrayAnalytics.d.ts.map