/**
 * ICT Action Plan Hook
 *
 * Generates data-driven ICT action items based on real trading performance.
 * Provides model recommendations, risk management, and quality control guidance.
 */
export interface WeeklyPerformance {
    totalTrades: number;
    winRate: number;
    avgRMultiple: number;
    avgQuality: number;
    totalPnL: number;
}
export interface ModelStrategy {
    recommendation: 'RD-Cont' | 'FVG-RD' | 'Either';
    reasoning: string;
    rdCont: {
        trades: number;
        winRate: number;
        avgR: number;
    };
    fvgRd: {
        trades: number;
        winRate: number;
        avgR: number;
    };
}
export interface ActionItem {
    description: string;
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
    category: 'MODEL' | 'SESSION' | 'QUALITY' | 'RISK';
}
export interface RiskManagement {
    avgRisk: number;
    targetRMultiple: number;
    positionSizing: string;
    stopLossStrategy: string;
}
export interface ICTActionPlanData {
    weeklyPerformance: WeeklyPerformance;
    modelStrategy: ModelStrategy;
    actionItems: ActionItem[];
    riskManagement: RiskManagement;
    qualityChecklist: string[];
    sessionGuidance: string[];
}
/**
 * ICT Action Plan Hook
 */
export declare const useICTActionPlan: () => {
    actionPlan: ICTActionPlanData | null;
    isLoading: boolean;
    error: string | null;
    refresh: () => void;
};
//# sourceMappingURL=useICTActionPlan.d.ts.map