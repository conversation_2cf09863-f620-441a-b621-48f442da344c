import { jsx as _jsx } from "react/jsx-runtime";
/**
 * Daily Guide Context
 *
 * Context for managing daily guide state
 */
import { createContext, useContext, useReducer, useCallback, useEffect, } from 'react';
import { DEFAULT_DAILY_GUIDE_STATE } from '../types';
import { fetchDailyGuideData } from '../api/dailyGuideApi';
// Initial state - use the correct default state
const initialState = DEFAULT_DAILY_GUIDE_STATE;
// Reducer function
const dailyGuideReducer = (state, action) => {
    switch (action.type) {
        case 'FETCH_DATA_START':
            return {
                ...state,
                isLoading: true,
                error: null,
            };
        case 'FETCH_DATA_SUCCESS':
            return {
                ...state,
                data: action.payload,
                isLoading: false,
                error: null,
            };
        case 'FETCH_DATA_ERROR':
            return {
                ...state,
                isLoading: false,
                error: action.payload,
            };
        case 'UPDATE_TRADING_PLAN_ITEM':
            // Update trading plan item if it exists
            if (!state.data.tradingPlan) {
                return state;
            }
            return {
                ...state,
                data: {
                    ...state.data,
                    tradingPlan: {
                        ...state.data.tradingPlan,
                        items: state.data.tradingPlan.items.map((item) => item.id === action.payload.id
                            ? { ...item, completed: action.payload.completed }
                            : item),
                    },
                },
            };
        case 'REFRESH_DATA':
            return {
                ...state,
                isLoading: true,
                error: null,
            };
        default:
            return state;
    }
};
const DailyGuideContext = createContext(undefined);
export const DailyGuideProvider = ({ children }) => {
    const [state, dispatch] = useReducer(dailyGuideReducer, initialState);
    const fetchGuideData = useCallback(async () => {
        dispatch({ type: 'FETCH_DATA_START' });
        try {
            const data = await fetchDailyGuideData();
            dispatch({ type: 'FETCH_DATA_SUCCESS', payload: data });
        }
        catch (error) {
            dispatch({
                type: 'FETCH_DATA_ERROR',
                payload: error instanceof Error ? error.message : 'An unknown error occurred',
            });
        }
    }, []);
    const updateTradingPlanItem = useCallback((id, completed) => {
        dispatch({ type: 'UPDATE_TRADING_PLAN_ITEM', payload: { id, completed } });
    }, []);
    const refreshData = useCallback(() => {
        dispatch({ type: 'REFRESH_DATA' });
        fetchGuideData();
    }, [fetchGuideData]);
    // Load data on mount
    useEffect(() => {
        fetchGuideData();
    }, [fetchGuideData]);
    const value = {
        ...state,
        fetchGuideData,
        updateTradingPlanItem,
        refreshData,
    };
    return _jsx(DailyGuideContext.Provider, { value: value, children: children });
};
// Custom hook for using the context
export const useDailyGuide = () => {
    const context = useContext(DailyGuideContext);
    if (context === undefined) {
        throw new Error('useDailyGuide must be used within a DailyGuideProvider');
    }
    return context;
};
//# sourceMappingURL=DailyGuideContext.js.map