import { DailyGuideState, MarketSentiment, TradingPlanPriority } from '../types';
export declare const selectDailyGuideData: (state: DailyGuideState) => import("../types").DailyGuideData;
export declare const selectMarketOverview: (state: DailyGuideState) => import("../types").MarketOverview | null;
export declare const selectTradingPlan: (state: DailyGuideState) => import("../types").TradingPlan | null;
export declare const selectKeyPriceLevels: (state: DailyGuideState) => import("../types").KeyPriceLevel[];
export declare const selectWatchlist: (state: DailyGuideState) => import("../types").WatchlistItem[];
export declare const selectMarketNews: (state: DailyGuideState) => import("../types").MarketNewsItem[];
export declare const selectIsLoading: (state: DailyGuideState) => boolean;
export declare const selectError: (state: DailyGuideState) => string | null;
export declare const selectSelectedDate: (state: DailyGuideState) => string;
export declare const selectTradingPlanItems: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").TradingPlanItem[]>;
export declare const selectCompletedTradingPlanItems: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").TradingPlanItem[]>;
export declare const selectIncompleteTradingPlanItems: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").TradingPlanItem[]>;
export declare const selectTradingPlanCompletion: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, number>;
export declare const selectTradingPlanItemsByPriority: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, Record<TradingPlanPriority, import("../types").TradingPlanItem[]>>;
export declare const selectMarketIndices: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").MarketIndex[]>;
export declare const selectPositiveIndices: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").MarketIndex[]>;
export declare const selectNegativeIndices: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").MarketIndex[]>;
export declare const selectMarketSentiment: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, MarketSentiment>;
export declare const selectMarketSummary: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, string>;
export declare const selectEconomicEvents: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").EconomicEvent[]>;
export declare const selectHighImpactEconomicEvents: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").EconomicEvent[]>;
export declare const selectKeyPriceLevelsBySymbol: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, Record<string, import("../types").KeyPriceLevel>>;
export declare const selectWatchlistBySymbol: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, Record<string, import("../types").WatchlistItem>>;
export declare const selectHighImpactMarketNews: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").MarketNewsItem[]>;
export declare const selectMarketNewsByDate: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, Record<string, import("../types").MarketNewsItem[]>>;
export declare const selectHasData: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, boolean>;
export declare const selectLastUpdated: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, string | null>;
export declare const dailyGuideSelectors: {
    selectDailyGuideData: (state: DailyGuideState) => import("../types").DailyGuideData;
    selectMarketOverview: (state: DailyGuideState) => import("../types").MarketOverview | null;
    selectTradingPlan: (state: DailyGuideState) => import("../types").TradingPlan | null;
    selectKeyPriceLevels: (state: DailyGuideState) => import("../types").KeyPriceLevel[];
    selectWatchlist: (state: DailyGuideState) => import("../types").WatchlistItem[];
    selectMarketNews: (state: DailyGuideState) => import("../types").MarketNewsItem[];
    selectIsLoading: (state: DailyGuideState) => boolean;
    selectError: (state: DailyGuideState) => string | null;
    selectSelectedDate: (state: DailyGuideState) => string;
    selectTradingPlanItems: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").TradingPlanItem[]>;
    selectCompletedTradingPlanItems: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").TradingPlanItem[]>;
    selectIncompleteTradingPlanItems: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").TradingPlanItem[]>;
    selectTradingPlanCompletion: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, number>;
    selectTradingPlanItemsByPriority: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, Record<TradingPlanPriority, import("../types").TradingPlanItem[]>>;
    selectMarketIndices: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").MarketIndex[]>;
    selectPositiveIndices: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").MarketIndex[]>;
    selectNegativeIndices: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").MarketIndex[]>;
    selectMarketSentiment: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, MarketSentiment>;
    selectMarketSummary: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, string>;
    selectEconomicEvents: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").EconomicEvent[]>;
    selectHighImpactEconomicEvents: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").EconomicEvent[]>;
    selectKeyPriceLevelsBySymbol: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, Record<string, import("../types").KeyPriceLevel>>;
    selectWatchlistBySymbol: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, Record<string, import("../types").WatchlistItem>>;
    selectHighImpactMarketNews: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, import("../types").MarketNewsItem[]>;
    selectMarketNewsByDate: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, Record<string, import("../types").MarketNewsItem[]>>;
    selectHasData: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, boolean>;
    selectLastUpdated: import("@adhd-trading-dashboard/shared/state/createSelector").Selector<DailyGuideState, string | null>;
};
//# sourceMappingURL=dailyGuideSelectors.d.ts.map