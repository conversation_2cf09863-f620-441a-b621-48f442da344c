import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { Card, DualTimeDisplay } from '@adhd-trading-dashboard/shared';
import { useSessionAnalytics, } from '../hooks/useSessionAnalytics';
import { useEnhancedSessionIntelligence } from '../hooks/useEnhancedSessionIntelligence';
// Styled components with F1 theme
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
`;
const CurrentSessionCard = styled.div `
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 1px solid #dc2626;
  border-radius: 8px;
  padding: 20px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #dc2626, #ef4444, #dc2626);
    animation: pulse 2s ease-in-out infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }
`;
const SessionHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;
const SessionTitle = styled.h3 `
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;
const LiveIndicator = styled.span `
  background: #dc2626;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  animation: blink 1.5s ease-in-out infinite;

  @keyframes blink {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.6;
    }
  }
`;
const RecommendationBadge = styled.div `
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  background: ${({ level }) => {
    switch (level) {
        case 'high':
            return 'linear-gradient(135deg, #10b981, #059669)';
        case 'medium':
            return 'linear-gradient(135deg, #f59e0b, #d97706)';
        case 'low':
            return 'linear-gradient(135deg, #ef4444, #dc2626)';
        case 'avoid':
            return 'linear-gradient(135deg, #7f1d1d, #991b1b)';
        default:
            return 'linear-gradient(135deg, #6b7280, #4b5563)';
    }
}};
`;
const MetricsGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;
const MetricCard = styled.div `
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
`;
const MetricValue = styled.div `
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
`;
const MetricLabel = styled.div `
  font-size: 12px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
const ActionItems = styled.div `
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  border-radius: 6px;
  padding: 16px;
`;
const ActionTitle = styled.h4 `
  color: #dc2626;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
const ActionList = styled.ul `
  margin: 0;
  padding: 0;
  list-style: none;
`;
const ActionItem = styled.li `
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;

  &::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: #dc2626;
    font-size: 12px;
  }

  &:last-child {
    margin-bottom: 0;
  }
`;
const SessionGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
  margin-top: 16px;
`;
const SessionBlock = styled.div `
  background: ${({ performance, isCurrent }) => {
    if (isCurrent)
        return 'linear-gradient(135deg, #dc2626, #ef4444)';
    switch (performance) {
        case 'excellent':
            return 'linear-gradient(135deg, #10b981, #059669)';
        case 'good':
            return 'linear-gradient(135deg, #3b82f6, #2563eb)';
        case 'average':
            return 'linear-gradient(135deg, #6b7280, #4b5563)';
        case 'poor':
            return 'linear-gradient(135deg, #f59e0b, #d97706)';
        case 'avoid':
            return 'linear-gradient(135deg, #ef4444, #dc2626)';
        default:
            return 'linear-gradient(135deg, #374151, #1f2937)';
    }
}};
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  position: relative;
  ${({ isCurrent }) => isCurrent &&
    `
    border: 2px solid #ffffff;
    box-shadow: 0 0 10px rgba(220, 38, 38, 0.5);
  `}
`;
const SessionHour = styled.div `
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
`;
const SessionWinRate = styled.div `
  font-size: 14px;
  font-weight: 700;
  color: #ffffff;
`;
const SessionLabel = styled.div `
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
`;
// Enhanced ICT Session Components
const ICTSessionCard = styled.div `
  background: ${({ isActive, isOptimal }) => {
    if (isActive && isOptimal)
        return 'linear-gradient(135deg, rgba(220, 38, 38, 0.2), rgba(239, 68, 68, 0.1))';
    if (isActive)
        return 'linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1))';
    return 'rgba(255, 255, 255, 0.05)';
}};
  border: 1px solid
    ${({ isActive, isOptimal }) => {
    if (isActive && isOptimal)
        return 'rgba(220, 38, 38, 0.4)';
    if (isActive)
        return 'rgba(59, 130, 246, 0.4)';
    return 'rgba(255, 255, 255, 0.1)';
}};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
`;
const ICTSessionHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;
const ICTSessionName = styled.div `
  font-size: 16px;
  font-weight: 700;
  color: ${({ isActive }) => (isActive ? '#dc2626' : '#ffffff')};
  display: flex;
  align-items: center;
  gap: 8px;
`;
const ICTTimeRange = styled.div `
  font-size: 12px;
  color: #9ca3af;
`;
const ICTPerformanceGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin: 12px 0;
`;
const ICTMetric = styled.div `
  text-align: center;
`;
const ICTMetricValue = styled.div `
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
`;
const ICTMetricLabel = styled.div `
  font-size: 10px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
const ModelPreferenceCard = styled.div `
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
`;
const ModelPreferenceHeader = styled.div `
  font-size: 12px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
`;
const ModelComparison = styled.div `
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
`;
const ModelStats = styled.div `
  background: ${({ isRecommended }) => isRecommended ? 'rgba(220, 38, 38, 0.1)' : 'rgba(255, 255, 255, 0.03)'};
  border: 1px solid
    ${({ isRecommended }) => isRecommended ? 'rgba(220, 38, 38, 0.3)' : 'rgba(255, 255, 255, 0.1)'};
  border-radius: 6px;
  padding: 8px;
  text-align: center;
`;
const ModelName = styled.div `
  font-size: 12px;
  font-weight: 600;
  color: ${({ isRecommended }) => (isRecommended ? '#dc2626' : '#ffffff')};
  margin-bottom: 4px;
`;
const ModelMetrics = styled.div `
  font-size: 10px;
  color: #9ca3af;
`;
const OptimalWindowsGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-top: 8px;
`;
const WindowCard = styled.div `
  background: ${({ isOptimal }) => isOptimal ? 'rgba(16, 185, 129, 0.1)' : 'rgba(255, 255, 255, 0.03)'};
  border: 1px solid
    ${({ isOptimal }) => (isOptimal ? 'rgba(16, 185, 129, 0.3)' : 'rgba(255, 255, 255, 0.1)')};
  border-radius: 6px;
  padding: 8px;
  text-align: center;
`;
const WindowTime = styled.div `
  font-size: 11px;
  font-weight: 600;
  color: #ffffff;
`;
const WindowDescription = styled.div `
  font-size: 9px;
  color: #9ca3af;
  margin: 2px 0;
`;
const WindowStats = styled.div `
  font-size: 9px;
  color: #d1d5db;
`;
const LiveIndicator = styled.div `
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: #dc2626;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &::before {
    content: '';
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;
const WeeklyInsightsCard = styled.div `
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(147, 51, 234, 0.05));
  border: 1px solid rgba(168, 85, 247, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
`;
const InsightsHeader = styled.div `
  font-size: 14px;
  font-weight: 700;
  color: #a855f7;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
const InsightsList = styled.div `
  display: flex;
  flex-direction: column;
  gap: 8px;
`;
const InsightItem = styled.div `
  font-size: 12px;
  color: #d1d5db;
  padding: 6px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &:last-child {
    border-bottom: none;
  }
`;
/**
 * Session Focus Component
 */
export const SessionFocus = ({ isLoading = false, error = null, onRefresh, className, }) => {
    const { analytics, isLoading: analyticsLoading, error: analyticsError } = useSessionAnalytics();
    const { intelligence, isLoading: enhancedLoading, error: enhancedError, } = useEnhancedSessionIntelligence();
    const loading = isLoading || analyticsLoading || enhancedLoading;
    const displayError = error || analyticsError || enhancedError;
    // Loading state
    if (loading) {
        return (_jsx(Card, { title: "\uD83C\uDFAF Session Focus", children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "Analyzing your trading sessions..." }) }));
    }
    // Error state
    if (displayError) {
        return (_jsx(Card, { title: "\uD83C\uDFAF Session Focus", children: _jsxs("div", { style: { padding: '24px', textAlign: 'center', color: '#f44336' }, children: ["Error: ", displayError, onRefresh && (_jsx("button", { onClick: onRefresh, style: {
                            marginLeft: '16px',
                            padding: '8px 16px',
                            background: '#f0f0f0',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                        }, children: "Retry" }))] }) }));
    }
    const { currentRecommendation, sessionPerformance, totalAnalyzedTrades } = analytics;
    return (_jsx(Card, { title: "\uD83D\uDD58 Enhanced Session Intelligence", actions: onRefresh ? (_jsx("button", { onClick: onRefresh, style: { background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }, children: "\uD83D\uDD04 Refresh" })) : undefined, children: _jsxs(Container, { className: className, children: [_jsxs(CurrentSessionCard, { children: [_jsxs(SessionHeader, { children: [_jsxs(SessionTitle, { children: [intelligence.currentStatus.currentRecommendation, intelligence.currentStatus.activeSession && _jsx(LiveIndicator, { children: "LIVE" })] }), _jsx(RecommendationBadge, { level: intelligence.currentStatus.urgency.toLowerCase(), children: intelligence.currentStatus.urgency })] }), _jsxs(MetricsGrid, { children: [_jsxs(MetricCard, { children: [_jsx(MetricValue, { children: _jsx(DualTimeDisplay, { mode: "current", format: "compact", showLive: true }) }), _jsx(MetricLabel, { children: "Current Time" })] }), _jsxs(MetricCard, { children: [_jsx(MetricValue, { children: intelligence.currentStatus.activeSession?.sessionName || 'None' }), _jsx(MetricLabel, { children: "Active Session" })] }), _jsxs(MetricCard, { children: [_jsx(MetricValue, { children: intelligence.currentStatus.timeToNextFormatted }), _jsx(MetricLabel, { children: "Next Session" })] })] })] }), intelligence.currentStatus.activeSession && (_jsxs(ICTSessionCard, { isActive: true, isOptimal: true, children: [_jsx(ICTSessionHeader, { children: _jsxs(ICTSessionName, { isActive: true, children: ["\u23F0 LIVE SESSION INTELLIGENCE", _jsx(LiveIndicator, { children: "LIVE SESSION" })] }) }), _jsxs("div", { style: { marginBottom: '16px' }, children: [_jsxs("h4", { style: { color: '#ffffff', marginBottom: '12px', fontSize: '16px' }, children: [intelligence.currentStatus.activeSession.sessionName, " - Active"] }), _jsx("div", { style: { display: 'grid', gap: '12px' }, children: intelligence.sessions
                                        .find((s) => s.sessionName === intelligence.currentStatus.activeSession?.sessionName)
                                        ?.optimalWindows.map((window, index) => (_jsxs(WindowCard, { isOptimal: window.winRate >= 70 && window.trades >= 2, children: [_jsx(WindowTime, { children: _jsx(DualTimeDisplay, { mode: "session", sessionStart: window.start, sessionEnd: window.end, format: "compact" }) }), _jsx(WindowDescription, { children: window.description }), _jsx(WindowStats, { children: window.trades > 0
                                                    ? `${window.winRate.toFixed(0)}% win rate (${window.trades} trades)`
                                                    : 'No data' })] }, index))) })] })] })), intelligence.sessions.map((session) => {
                    const isActive = intelligence.currentStatus.activeSession?.sessionName === session.sessionName;
                    const hasOptimalWindow = session.optimalWindows.some((w) => w.winRate >= 70);
                    return (_jsxs(ICTSessionCard, { isActive: isActive, isOptimal: hasOptimalWindow, children: [_jsxs(ICTSessionHeader, { children: [_jsxs(ICTSessionName, { isActive: isActive, children: [session.sessionName, isActive && _jsx(LiveIndicator, { children: "ACTIVE" })] }), _jsx(ICTTimeRange, { children: session.timeRange ? (_jsx(DualTimeDisplay, { mode: "session", sessionStart: session.timeRange.split('-')[0], sessionEnd: session.timeRange.split('-')[1], format: "compact" })) : (_jsx("span", { children: "Time range not available" })) })] }), _jsxs(ICTPerformanceGrid, { children: [_jsxs(ICTMetric, { children: [_jsx(ICTMetricValue, { children: session.performance.totalTrades }), _jsx(ICTMetricLabel, { children: "Trades" })] }), _jsxs(ICTMetric, { children: [_jsxs(ICTMetricValue, { children: [session.performance.winRate.toFixed(0), "%"] }), _jsx(ICTMetricLabel, { children: "Win Rate" })] }), _jsxs(ICTMetric, { children: [_jsxs(ICTMetricValue, { children: [session.performance.avgRMultiple.toFixed(1), "R"] }), _jsx(ICTMetricLabel, { children: "Avg R-Multiple" })] }), _jsxs(ICTMetric, { children: [_jsx(ICTMetricValue, { children: session.performance.avgRisk.toFixed(0) }), _jsx(ICTMetricLabel, { children: "Avg Risk (pts)" })] })] }), _jsxs("div", { children: [_jsx(ModelPreferenceHeader, { children: "Optimal Time Windows" }), _jsx(OptimalWindowsGrid, { children: session.optimalWindows.map((window, index) => (_jsxs(WindowCard, { isOptimal: window.winRate >= 70 && window.trades >= 2, children: [_jsx(WindowTime, { children: _jsx(DualTimeDisplay, { mode: "session", sessionStart: window.start, sessionEnd: window.end, format: "compact" }) }), _jsx(WindowDescription, { children: window.description }), _jsx(WindowStats, { children: window.trades > 0
                                                        ? `${window.winRate.toFixed(0)}% (${window.trades} trades)`
                                                        : 'No data' })] }, index))) })] }), session.recommendations.length > 0 && (_jsxs(ActionItems, { children: [_jsx(ActionTitle, { children: "Session Recommendations" }), _jsx(ActionList, { children: session.recommendations.map((rec, index) => (_jsx(ActionItem, { children: rec }, index))) })] }))] }, session.sessionName));
                }), _jsxs(WeeklyInsightsCard, { children: [_jsx(InsightsHeader, { children: "\uD83D\uDCCA Weekly Performance Insights" }), _jsxs(InsightsList, { children: [_jsxs(InsightItem, { children: [_jsx("strong", { children: "Best Session:" }), " ", intelligence.weeklyInsights.bestSession] }), _jsxs(InsightItem, { children: [_jsx("strong", { children: "Best Model:" }), " ", intelligence.weeklyInsights.bestModel] }), _jsxs(InsightItem, { children: [_jsx("strong", { children: "Average Quality:" }), " ", intelligence.weeklyInsights.avgQuality.toFixed(1), "/5.0"] }), _jsxs(InsightItem, { children: [_jsx("strong", { children: "Quality Threshold:" }), ' ', `>${intelligence.weeklyInsights.qualityThreshold.toFixed(1)}`, " for optimal performance"] })] }), _jsxs(ActionItems, { style: { marginTop: '12px' }, children: [_jsx(ActionTitle, { children: "Key Recommendations" }), _jsx(ActionList, { children: intelligence.weeklyInsights.recommendations.map((rec, index) => (_jsx(ActionItem, { children: rec }, index))) })] })] })] }) }));
};
//# sourceMappingURL=SessionFocus.js.map