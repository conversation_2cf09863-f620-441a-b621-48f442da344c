/**
 * F1JournalHeader Component
 *
 * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)
 * F1 racing-themed header for the trade journal feature.
 *
 * BENEFITS:
 * - Focused responsibility (header only)
 * - F1 racing theme with journal-specific indicators
 * - Consistent with other F1Header components
 * - Better separation of concerns
 * - Reusable across journal contexts
 */
import React from 'react';
export interface F1JournalHeaderProps {
    /** Custom className */
    className?: string;
    /** Whether data is loading */
    isLoading?: boolean;
    /** Whether refresh is in progress */
    isRefreshing?: boolean;
    /** Number of trades */
    tradeCount?: number;
    /** Number of filtered trades */
    filteredCount?: number;
    /** Whether filters are active */
    hasActiveFilters?: boolean;
    /** Refresh handler */
    onRefresh?: () => void;
    /** Export handler */
    onExport?: () => void;
    /** Import handler */
    onImport?: () => void;
    /** Custom title */
    title?: string;
}
/**
 * F1JournalHeader Component
 *
 * PATTERN: F1 Header Pattern
 * - Racing-inspired styling with journal-specific indicators
 * - Trade count and filter status indicators
 * - Consistent with F1 design system
 * - Accessible and responsive
 * - Professional trading journal appearance
 */
export declare const F1JournalHeader: React.FC<F1JournalHeaderProps>;
export default F1JournalHeader;
//# sourceMappingURL=F1JournalHeader.d.ts.map