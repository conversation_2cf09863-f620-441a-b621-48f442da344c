/**
 * COMPREHENSIVE TIME DATA FLOW ANALYSIS
 *
 * This script traces the complete time flow through the Daily Guide system
 * to identify why current time displays correctly while NY trading sessions show incorrect timing.
 */
// Simulate the core time utilities
const getUserTimezone = () => {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
};
const getTimezoneAbbreviation = (timezone, date = new Date()) => {
    const formatter = new Intl.DateTimeFormat('en', {
        timeZone: timezone,
        timeZoneName: 'short',
    });
    const parts = formatter.formatToParts(date);
    const timeZonePart = parts.find((part) => part.type === 'timeZoneName');
    return timeZonePart?.value || timezone;
};
const convertNYToLocal = (nyTime, userTimezone) => {
    const timezone = userTimezone || getUserTimezone();
    // Parse the NY time
    const [hours, minutes] = nyTime.split(':').map(Number);
    // Get today's date
    const today = new Date();
    // Create a date for today at the specified time
    const baseDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes, 0);
    // The key insight: we need to create a date that when interpreted in NY timezone gives us our desired time
    // We'll use the inverse approach - create what would be the UTC time for this NY time
    // First, let's see what time it would be in NY if we interpret our base date as local time
    const baseInNY = new Date(baseDate.toLocaleString('en-US', { timeZone: 'America/New_York' }));
    const baseInUTC = new Date(baseDate.toLocaleString('en-US', { timeZone: 'UTC' }));
    // Calculate the difference
    const offsetMs = baseInUTC.getTime() - baseInNY.getTime();
    // Apply this offset to get the correct UTC time
    const correctUTCTime = new Date(baseDate.getTime() + offsetMs);
    // Now convert this UTC time to the user's timezone
    return correctUTCTime.toLocaleTimeString('en-GB', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
};
const getCurrentDualTime = (userTimezone) => {
    const timezone = userTimezone || getUserTimezone();
    const now = new Date();
    const nyTime = now.toLocaleTimeString('en-US', {
        timeZone: 'America/New_York',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
    const localTime = now.toLocaleTimeString('en-GB', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
    const nyTimezone = getTimezoneAbbreviation('America/New_York', now);
    const localTimezone = getTimezoneAbbreviation(timezone, now);
    return {
        nyTime,
        localTime,
        nyTimezone,
        localTimezone,
        formatted: `${nyTime} ${nyTimezone} | ${localTime} ${localTimezone}`,
    };
};
const getCurrentNYMinutes = () => {
    const now = new Date();
    const nyTime = now.toLocaleTimeString('en-US', {
        timeZone: 'America/New_York',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
    const [hours, minutes] = nyTime.split(':').map(Number);
    return hours * 60 + minutes;
};
const timeToMinutes = (timeStr) => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
};
const formatTimeInterval = (totalMinutes) => {
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    let formatted = '';
    if (hours > 0) {
        formatted = minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
    else {
        formatted = `${minutes}m`;
    }
    return {
        totalMinutes,
        hours,
        minutes,
        formatted,
    };
};
const convertSessionToDualTime = (nyStart, nyEnd, userTimezone) => {
    const timezone = userTimezone || getUserTimezone();
    const localStart = convertNYToLocal(nyStart, timezone);
    const localEnd = convertNYToLocal(nyEnd, timezone);
    const localTimezoneAbbr = getTimezoneAbbreviation(timezone);
    return {
        nyStart,
        nyEnd,
        localStart,
        localEnd,
        formatted: `${nyStart}-${nyEnd} NY | ${localStart}-${localEnd} ${localTimezoneAbbr}`,
    };
};
// COMPREHENSIVE ANALYSIS
console.log('🔍 COMPREHENSIVE TIME DATA FLOW ANALYSIS');
console.log('==========================================');
const now = new Date();
const userTimezone = getUserTimezone();
console.log('\n📍 SYSTEM CONTEXT:');
console.log('User Timezone:', userTimezone);
console.log('UTC Time:', now.toISOString());
console.log('Local Time:', now.toLocaleString());
console.log('\n⏰ CURRENT TIME FLOW (Working Correctly):');
const dualTime = getCurrentDualTime();
console.log('NY Time:', dualTime.nyTime, dualTime.nyTimezone);
console.log('Local Time:', dualTime.localTime, dualTime.localTimezone);
console.log('Formatted:', dualTime.formatted);
console.log('\n🎯 SESSION TIMING FLOW (Problematic):');
const currentNYMinutes = getCurrentNYMinutes();
console.log('Current NY Minutes:', currentNYMinutes);
// Hardcoded session times from useEnhancedSessionIntelligence
const sessions = [
    { name: 'Pre-Market', start: '08:00', end: '09:30' },
    { name: 'NY Open', start: '09:30', end: '11:00' },
    { name: 'Lunch Macro', start: '11:50', end: '13:30' },
    { name: 'MOC', start: '15:15', end: '16:00' }
];
console.log('\n📊 SESSION ANALYSIS:');
sessions.forEach(session => {
    const startMinutes = timeToMinutes(session.start);
    const endMinutes = timeToMinutes(session.end);
    const isActive = currentNYMinutes >= startMinutes && currentNYMinutes <= endMinutes;
    const timeToStart = startMinutes > currentNYMinutes ? startMinutes - currentNYMinutes : 0;
    // Convert session times to dual timezone
    const sessionTime = convertSessionToDualTime(session.start, session.end);
    console.log(`\n${session.name}:`);
    console.log(`  NY Time: ${session.start}-${session.end}`);
    console.log(`  Local Time: ${sessionTime.localStart}-${sessionTime.localEnd}`);
    console.log(`  Start Minutes: ${startMinutes}`);
    console.log(`  Current Minutes: ${currentNYMinutes}`);
    console.log(`  Active: ${isActive ? 'YES' : 'NO'}`);
    if (timeToStart > 0) {
        console.log(`  Time to Start: ${formatTimeInterval(timeToStart).formatted}`);
    }
});
console.log('\n🔍 TIMEZONE CONVERSION TEST:');
console.log('Testing convertNYToLocal function:');
console.log('09:30 NY →', convertNYToLocal('09:30', userTimezone));
console.log('11:50 NY →', convertNYToLocal('11:50', userTimezone));
console.log('15:15 NY →', convertNYToLocal('15:15', userTimezone));
console.log('\n🚨 POTENTIAL ISSUES TO INVESTIGATE:');
console.log('1. convertNYToLocal timezone conversion logic');
console.log('2. Session time hardcoding vs dynamic calculation');
console.log('3. Different time sources between current time and session times');
console.log('4. Timezone abbreviation handling');
console.log('\n✅ EXPECTED BEHAVIOR (Irish Summer Time):');
console.log('09:30 NY should be → 14:30 Irish (5 hour difference)');
console.log('11:50 NY should be → 16:50 Irish (5 hour difference)');
console.log('15:15 NY should be → 20:15 Irish (5 hour difference)');
//# sourceMappingURL=time-flow-analysis.js.map