/**
 * Test the timezone conversion fix
 */
// Simulate the fixed convertNYToLocal function
const getUserTimezone = () => {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
};
const convertNYToLocal = (nyTime, userTimezone) => {
    const timezone = userTimezone || getUserTimezone();
    // Parse the NY time
    const [hours, minutes] = nyTime.split(':').map(Number);
    // FIXED: Use the simplest and most reliable approach
    // Create a date representing the current day
    const today = new Date();
    // Create a date string in ISO format for today at the specified time
    // We'll treat this as if it's in NY timezone
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const timeStr = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:00`;
    // Create the datetime string
    const isoString = `${year}-${month}-${day}T${timeStr}`;
    // Parse this as a local date first
    const localDate = new Date(isoString);
    // Now we need to adjust for the fact that we want this to represent NY time
    // Get current offset between local time and NY time
    const nowLocal = new Date();
    const nowInNY = new Date(nowLocal.toLocaleString('en-US', { timeZone: 'America/New_York' }));
    const nowInLocal = new Date(nowLocal.toLocaleString('en-US', { timeZone: timezone }));
    // Calculate the offset in milliseconds
    const offsetMs = nowInLocal.getTime() - nowInNY.getTime();
    // Apply this offset to our target time
    const adjustedDate = new Date(localDate.getTime() + offsetMs);
    // Format in user's timezone
    return adjustedDate.toLocaleTimeString('en-GB', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
    });
};
console.log('🧪 TESTING TIMEZONE CONVERSION FIX');
console.log('==================================');
const now = new Date();
const userTimezone = getUserTimezone();
console.log('Current Context:');
console.log('User Timezone:', userTimezone);
console.log('Current NY Time:', now.toLocaleTimeString('en-US', { timeZone: 'America/New_York', hour12: false }));
console.log('Current Local Time:', now.toLocaleTimeString('en-GB', { timeZone: userTimezone, hour12: false }));
console.log('\n🔧 TESTING FIXED CONVERSION:');
console.log('09:30 NY →', convertNYToLocal('09:30', userTimezone), '(Expected: 14:30 for Irish Summer Time)');
console.log('11:50 NY →', convertNYToLocal('11:50', userTimezone), '(Expected: 16:50 for Irish Summer Time)');
console.log('15:15 NY →', convertNYToLocal('15:15', userTimezone), '(Expected: 20:15 for Irish Summer Time)');
console.log('\n✅ EXPECTED RESULTS:');
console.log('- NY Open (09:30) should show 14:30 Irish time');
console.log('- Lunch Macro (11:50) should show 16:50 Irish time');
console.log('- MOC (15:15) should show 20:15 Irish time');
console.log('- All times should be 5 hours ahead of NY time (EDT to Irish Summer Time)');
//# sourceMappingURL=test-fix.js.map