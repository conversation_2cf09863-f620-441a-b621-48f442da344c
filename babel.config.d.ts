export let presets: ((string | {
    targets: {
        browsers: string[];
    };
    useBuiltIns: string;
    corejs: number;
})[] | (string | {
    runtime: string;
})[] | (string | {
    isTSX: boolean;
    allExtensions: boolean;
})[])[];
export let plugins: string[];
export namespace env {
    namespace production {
        let plugins_1: (string | {
            displayName: boolean;
            pure: boolean;
        })[][];
        export { plugins_1 as plugins };
    }
    namespace development {
        let plugins_2: (string | {
            displayName: boolean;
            fileName: boolean;
        })[][];
        export { plugins_2 as plugins };
    }
}
//# sourceMappingURL=babel.config.d.ts.map